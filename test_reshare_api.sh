#!/bin/bash

# Test script for isReshared flag functionality
BASE_URL="http://localhost:8092/synergy/api"
PR_ID="822"  # Change this to an actual PR ID

echo "=== Testing isReshared Flag Functionality ==="
echo ""

# Step 1: Get current state of PR
echo "1. Getting current state of Purchase Request $PR_ID..."
curl -X GET "$BASE_URL/purchase-requests/$PR_ID" \
  -H "Content-Type: application/json" \
  | jq '.isReshared // "field not found"'

echo ""
echo ""

# Step 2: Get vendors for this PR to know which vendor IDs to use
echo "2. Getting vendors for Purchase Request $PR_ID..."
curl -X GET "$BASE_URL/purchase-requests/$PR_ID/vendors" \
  -H "Content-Type: application/json" \
  | jq '.[].srNo'

echo ""
echo ""

# Step 3: Resend RFQ emails (you'll need to update vendor IDs based on step 2)
echo "3. Resending RFQ emails..."
echo "Note: Update the vendorIds array below with actual vendor IDs from step 2"
echo ""
echo "Example command:"
echo "curl -X POST \"$BASE_URL/purchase-requests/$PR_ID/resend-rfq-emails\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"vendorIds\": [1, 2]}'"

echo ""
echo ""

# Step 4: Check if isReshared flag is now true
echo "4. After resending, check if isReshared flag is set to true..."
echo "curl -X GET \"$BASE_URL/purchase-requests/$PR_ID\" | jq '.isReshared'"

echo ""
echo "=== Test Complete ==="
