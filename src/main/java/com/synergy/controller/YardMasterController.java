package com.synergy.controller;

import com.synergy.dto.YardMasterDTO;
import com.synergy.dto.YardMasterDropdownDTO;
import com.synergy.entity.YardMasterEntity;
import com.synergy.repository.YardMasterRepository;
import com.synergy.service.YardMasterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/yard-master")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Yard Master Management", description = "APIs for managing yard master data")
public class YardMasterController {

    private static final Logger logger = LoggerFactory.getLogger(YardMasterController.class);

    @Autowired
    private YardMasterService yardMasterService;

    @Autowired
    private YardMasterRepository yardMasterRepository;
    
    @GetMapping
    public ResponseEntity<?> getAllYards() {
        try {
            List<YardMasterDTO> yards = yardMasterService.getAllYards();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", yards);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error retrieving yards: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<?> getYardById(@PathVariable Long id) {
        try {
            Optional<YardMasterDTO> yardOpt = yardMasterService.getYardById(id);
            if (yardOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", yardOpt.get());
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Yard not found with ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error retrieving yard: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/by-yard-number/{yardNumber}")
    public ResponseEntity<?> getYardByYardNumber(@PathVariable String yardNumber) {
        try {
            Optional<YardMasterDTO> yardOpt = yardMasterService.getYardByYardNumber(yardNumber);
            if (yardOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", yardOpt.get());
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Yard not found with yard number: " + yardNumber);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error retrieving yard: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @PostMapping
    public ResponseEntity<?> createYard(@Valid @RequestBody YardMasterDTO yardDTO) {
        try {
            YardMasterDTO createdYard = yardMasterService.createYard(yardDTO);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Yard created successfully");
            response.put("data", createdYard);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error creating yard: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateYard(@PathVariable Long id, @Valid @RequestBody YardMasterDTO yardDTO) {
        try {
            YardMasterDTO updatedYard = yardMasterService.updateYard(id, yardDTO);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Yard updated successfully");
            response.put("data", updatedYard);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error updating yard: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteYard(@PathVariable Long id) {
        try {
            yardMasterService.deleteYard(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Yard deleted successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error deleting yard: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/dropdown")
    public ResponseEntity<?> getYardsForDropdown() {
        try {
            List<YardMasterDropdownDTO> yards = yardMasterService.getYardsForDropdown();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", yards);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error retrieving yards for dropdown: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }



    @GetMapping("/search")
    public ResponseEntity<?> searchYards(
            @RequestParam(required = false) String yardNumber,
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String projectName) {
        try {
            List<YardMasterDTO> yards = yardMasterService.searchYards(yardNumber, clientName, projectName);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", yards);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error searching yards: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/export/excel")
    @Operation(summary = "Export items to Excel", description = "Exports all items to an Excel file")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Excel export successful"),
            @ApiResponse(responseCode = "500", description = "Export failed")})
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<YardMasterEntity> items = yardMasterRepository.findAll();
            yardMasterService.exportYardMasterToExcel(items, response);
        } catch (Exception e) {
            logger.error("Error exporting items to Excel", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/pdf")
    @Operation(summary = "Export yard master to PDF", description = "Exports all yard master to a PDF file")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "PDF export successful"),
            @ApiResponse(responseCode = "500", description = "Export failed")})
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<YardMasterEntity> items = yardMasterRepository.findAll();
            yardMasterService.exportYardMasterToPdf(items, response);
        } catch (Exception e) {
            logger.error("Error exporting yard master to PDF", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
