package com.synergy.controller;

import com.synergy.dto.PdfGenerationFormDTO;
import com.synergy.dto.PurchaseOrderDeliveryTermsDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.service.PdfGenerationFormService;
import com.synergy.service.PurchaseRequestService;
import com.synergy.service.ExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/pdf-generation")
@Tag(name = "PDF Generation Form", description = "APIs for PDF generation form management")
public class PdfGenerationFormController {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfGenerationFormController.class);
    
    @Autowired
    private PdfGenerationFormService pdfGenerationFormService;

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @Autowired
    private ExportService exportService;
    
    /**
     * Get PDF generation form data for a vendor PO
     */
    @GetMapping("/vendor-po/{vendorPoId}/form-data")
    @Operation(
        summary = "Get PDF generation form data",
        description = "Retrieves form data for PDF generation with priority logic: " +
                "1. Vendor revised data (if exists), " +
                "2. Management edited data (if exists), " +
                "3. Original quotation data. " +
                "Used to populate the PDF generation form when user clicks action button from approved PO dashboard.",
        tags = {"PDF Generation Form"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Form data retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Vendor PO not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPdfGenerationFormData(
            @Parameter(description = "Vendor PO ID (e.g., 647-1)", required = true, example = "647-1")
            @PathVariable String vendorPoId) {
        try {
            logger.info("Getting PDF generation form data for vendor PO: {}", vendorPoId);
            
            PdfGenerationFormDTO formData = pdfGenerationFormService.getPdfGenerationFormData(vendorPoId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", formData);
            response.put("message", "PDF generation form data retrieved successfully");
            response.put("dataSource", formData.getDataSource());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.error("Invalid vendor PO ID: {}", vendorPoId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            
        } catch (Exception e) {
            logger.error("Error getting PDF generation form data for vendor PO: {}", vendorPoId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve PDF generation form data: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * Save management edited data and generate document (Single API for View PDF/Document button)
     */
    @PostMapping("/vendor-po/{vendorPoId}/view-pdf")
    @Operation(
        summary = "Save form data and generate document",
        description = "Single API for 'View PDF/Document' button. Saves the complete form data (delivery terms + edited line items) " +
                "to database and generates PDF or Word document based on format parameter. The edited data is stored for future reference.",
        tags = {"PDF Generation Form"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Document generated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid form data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> viewPdf(
            @Parameter(description = "Vendor PO ID (e.g., 647-1)", required = true, example = "647-1")
            @PathVariable String vendorPoId,
            @Parameter(description = "Document format (pdf or docx)", required = false, example = "pdf")
            @RequestParam(defaultValue = "pdf") String format,
            @RequestBody PdfGenerationFormDTO formData,
            HttpServletResponse response) {
        try {
            logger.info("Processing View {} request for vendor PO: {}", format.toUpperCase(), vendorPoId);

            // Ensure vendor PO ID matches
            formData.setVendorPoId(vendorPoId);

            // Save the edited data first
            pdfGenerationFormService.saveManagementEditedData(formData);

            // Get the saved form data (which now includes the management edited data)
            PdfGenerationFormDTO savedFormData = pdfGenerationFormService.getPdfGenerationFormData(vendorPoId);

            // Convert saved form data to delivery terms
            PurchaseOrderDeliveryTermsDTO deliveryTerms = convertFormToDeliveryTerms(savedFormData);

            // Get basic vendor PO structure but use saved form data for content
            VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);

            // Override vendor PO data with saved form data
            overrideVendorPOWithFormData(vendorPO, savedFormData);

            // Generate document based on format parameter
            if ("docx".equalsIgnoreCase(format)) {
                // Generate Word document
                exportService.generateVendorPurchaseOrderDocx(vendorPO, deliveryTerms, response);
            } else {
                // Generate PDF (default)
                exportService.generateVendorPurchaseOrderPdf(vendorPO, deliveryTerms, response);
            }

            return ResponseEntity.ok().build();

        } catch (IllegalArgumentException e) {
            logger.error("Invalid form data for vendor PO: {}", vendorPoId, e);
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", false);
            responseData.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(responseData);

        } catch (Exception e) {
            logger.error("Error processing View PDF request for vendor PO: {}", vendorPoId, e);
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", false);
            responseData.put("message", "Failed to generate PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(responseData);
        }
    }

    /**
     * Helper method to convert form data to delivery terms
     */
    private PurchaseOrderDeliveryTermsDTO convertFormToDeliveryTerms(PdfGenerationFormDTO formData) {
        PurchaseOrderDeliveryTermsDTO deliveryTerms = new PurchaseOrderDeliveryTermsDTO();
        deliveryTerms.setCurrency(formData.getCurrency());
        deliveryTerms.setTaxPercentage(formData.getTaxPercentage());
        deliveryTerms.setPaymentTerms(formData.getPaymentTerms());
        deliveryTerms.setDeliveryDate(formData.getDeliveryDate());
        deliveryTerms.setInvoiceAddress(formData.getInvoiceAddress());
        deliveryTerms.setDeliveryAddress(formData.getDeliveryAddress());
        deliveryTerms.setSoldToAddress(formData.getSoldToAddress());
        deliveryTerms.setDeliveryTerms(formData.getDeliveryTerms());
        deliveryTerms.setPurchaseRequestId(formData.getOriginalPrId());

        // Extract advance payment percentage from payment terms if needed
        if (formData.getPaymentTerms() != null && formData.getPaymentTerms().contains("%")) {
            try {
                String[] parts = formData.getPaymentTerms().split("%");
                if (parts.length > 0) {
                    String percentStr = parts[0].replaceAll("[^0-9]", "");
                    if (!percentStr.isEmpty()) {
                        deliveryTerms.setAdvancePaymentPercentage(percentStr);
                    }
                }
            } catch (Exception e) {
                logger.warn("Could not extract advance payment percentage from: {}", formData.getPaymentTerms());
            }
        }

        return deliveryTerms;
    }

    /**
     * Helper method to override vendor PO data with form data
     */
    private void overrideVendorPOWithFormData(VendorSplitPODTO vendorPO, PdfGenerationFormDTO formData) {
        // Override with form data values
        if (formData.getCurrency() != null) {
            vendorPO.setCurrency(formData.getCurrency());
        }
        if (formData.getGrandTotal() != null) {
            vendorPO.setVendorGrandTotal(formData.getGrandTotal());
        }
        if (formData.getRevisedGrandTotal() != null) {
            vendorPO.setVendorRevisedGrandTotal(formData.getRevisedGrandTotal());
        }

        // Override line items if present in form data
        if (formData.getLineItems() != null && !formData.getLineItems().isEmpty()) {
            // Convert form line items to vendor PO line items
            vendorPO.getLineItems().clear();
            formData.getLineItems().forEach(formItem -> {
                com.synergy.dto.ApprovedPOLineItemDTO poItem = new com.synergy.dto.ApprovedPOLineItemDTO();
                poItem.setUniqueCode(formItem.getUniqueCode());
                poItem.setItemCode(formItem.getItemCode());
                poItem.setDescription(formItem.getDescription());
                poItem.setMaterialFamily(formItem.getMaterialFamily());
                poItem.setQuantity(formItem.getQuantity());
                poItem.setUnitOfMeasure(formItem.getUnitOfMeasure());
                poItem.setRateApproved(formItem.getRateApproved());
                poItem.setTotal(formItem.getTotal());
                vendorPO.getLineItems().add(poItem);
            });
        }
    }
}
