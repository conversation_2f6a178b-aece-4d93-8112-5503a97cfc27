package com.synergy.controller;

import com.synergy.dto.VendorRevisedQuotationDTO;
import com.synergy.service.VendorRevisedQuotationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/vendor-revised-quotations")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
        RequestMethod.DELETE, RequestMethod.OPTIONS })
@Tag(name = "Vendor Revised Quotations", description = "APIs for managing vendor revised quotations (post-approval price negotiations)")
public class VendorRevisedQuotationController {

    private static final Logger logger = LoggerFactory.getLogger(VendorRevisedQuotationController.class);

    @Autowired
    private VendorRevisedQuotationService vendorRevisedQuotationService;

    /**
     * Submit a revised quotation (vendor edit form submission)
     */
    @PostMapping
    @Operation(summary = "Submit vendor revised quotation", 
               description = "Submit a revised quotation from vendor edit form. This is used for post-approval price negotiations. Requires a valid bidding token for one-time access.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Revised quotation submitted successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input or business rule violation"),
        @ApiResponse(responseCode = "403", description = "Invalid or expired token"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> submitRevisedQuotation(
            @RequestBody VendorRevisedQuotationDTO dto,
            @Parameter(description = "Bidding token for one-time access", required = false) 
            @RequestParam(required = false) String token) {
        try {
            logger.info("Submitting revised quotation for PR {} from vendor {} with token {}",
                    dto.getPurchaseRequestId(), dto.getVendorId(), token != null ? "provided" : "not provided");

            VendorRevisedQuotationDTO savedQuotation = vendorRevisedQuotationService.submitRevisedQuotation(dto, token);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedQuotation);
            response.put("message", "Revised quotation submitted successfully. This bidding link is now invalid.");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid revised quotation submission", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (SecurityException e) {
            logger.error("Security error in revised quotation submission", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        } catch (Exception e) {
            logger.error("Unexpected error in revised quotation submission", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "An unexpected error occurred while submitting the revised quotation.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get revised quotations for a purchase request (for PDF generation dashboard)
     */
    @GetMapping("/pr/{prId}")
    @Operation(summary = "Get revised quotations for a purchase request", 
               description = "Retrieves all revised quotations for a specific purchase request. Used by PDF generation dashboard.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Revised quotations retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid purchase request ID"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getRevisedQuotationsForPR(
            @Parameter(description = "Purchase Request ID", required = true)
            @PathVariable Long prId) {
        try {
            // Add validation
            if (prId == null || prId <= 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Invalid Purchase Request ID");
                return ResponseEntity.badRequest().body(response);
            }

            logger.info("Getting revised quotations for PR {}", prId);
            List<VendorRevisedQuotationDTO> quotations = vendorRevisedQuotationService.getRevisedQuotationsForPR(prId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", quotations);
            response.put("count", quotations.size());
            response.put("message", quotations.isEmpty() ? "No revised quotations found" : "Revised quotations retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error getting revised quotations for PR {}", prId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch revised quotations: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get revised quotation by ID (for PDF generation)
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get revised quotation by ID", 
               description = "Retrieves a specific revised quotation by its ID. Used for PDF generation.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Revised quotation retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid revised quotation ID"),
        @ApiResponse(responseCode = "404", description = "Revised quotation not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getRevisedQuotationById(
            @Parameter(description = "Revised Quotation ID", required = true)
            @PathVariable Long id) {
        try {
            // Add validation
            if (id == null || id <= 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Invalid Revised Quotation ID");
                return ResponseEntity.badRequest().body(response);
            }

            logger.info("Getting revised quotation with ID {}", id);
            VendorRevisedQuotationDTO quotation = vendorRevisedQuotationService.getRevisedQuotationById(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", quotation);
            response.put("message", "Revised quotation retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Revised quotation not found with ID {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            logger.error("Error getting revised quotation with ID {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch revised quotation: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get revised quotation by vendor PO ID (for edit form view)
     */
    @GetMapping("/vendor-po/{vendorPoId}")
    @Operation(summary = "Get revised quotation by vendor PO ID",
               description = "Retrieves the revised quotation submitted by a specific vendor PO (e.g., 890-1, 997-2). Used for displaying submitted edit form data.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Revised quotation retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid vendor PO ID"),
        @ApiResponse(responseCode = "404", description = "No revised quotation found for this vendor PO"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getRevisedQuotationByVendorPoId(
            @Parameter(description = "Vendor PO ID (e.g., 890-1, 997-2)", required = true, example = "890-1")
            @PathVariable String vendorPoId) {
        try {
            // Validate vendor PO ID format
            if (vendorPoId == null || vendorPoId.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Invalid vendor PO ID");
                return ResponseEntity.badRequest().body(response);
            }

            logger.info("Getting revised quotation for vendor PO {}", vendorPoId);
            VendorRevisedQuotationDTO quotation = vendorRevisedQuotationService.getRevisedQuotationByVendorPoId(vendorPoId);

            if (quotation != null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", quotation);
                response.put("message", "Revised quotation retrieved successfully");
                response.put("vendorPoId", vendorPoId);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "No revised quotation found for vendor PO " + vendorPoId);
                response.put("vendorPoId", vendorPoId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (IllegalArgumentException e) {
            logger.error("Invalid vendor PO ID {}", vendorPoId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("Error getting revised quotation for vendor PO {}", vendorPoId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch revised quotation: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
