package com.synergy.controller;

import com.itextpdf.text.DocumentException;
import com.synergy.entity.MaterialRequisitionEntity;
import com.synergy.entity.MaterialRequisitionItemEntity;
import com.synergy.repository.MaterialRequisitionRepository;
import com.synergy.service.ExportService;
import com.synergy.service.PurchaseRequestService;
import com.synergy.dto.ResponseDto;
import jakarta.servlet.http.HttpServletResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/api/requisitions")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
public class MaterialRequisitionController {

    @Autowired
    private MaterialRequisitionRepository materialRequisitionRepository;

    @Autowired
    private ExportService exportService;

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @PostMapping("/create")
    public ResponseEntity<?> createRequisition(@RequestBody Map<String, Object> requestMap) {
        try {
            // Create a new entity from the request map
            MaterialRequisitionEntity requisition = new MaterialRequisitionEntity();

            // Extract fields from the request map
            if (requestMap.containsKey("yardNumber")) {
                requisition.setYardNumber((String) requestMap.get("yardNumber"));
            }

            if (requestMap.containsKey("contractorName")) {
                requisition.setContractorName((String) requestMap.get("contractorName"));
            }

            if (requestMap.containsKey("projectName")) {
                requisition.setProjectName((String) requestMap.get("projectName"));
            }

            if (requestMap.containsKey("priority")) {
                requisition.setPriority((String) requestMap.get("priority"));
            }

            // Get the count of existing requisitions and generate next ID
            long count = materialRequisitionRepository.count() + 1;
            String requisitionId = String.format("%02d", count);

            requisition.setRequisitionId(requisitionId);
            requisition.setCreatedDate(new Date());
            requisition.setStatus("PENDING");

            // Handle line items if present
            if (requestMap.containsKey("lineItems") && requestMap.get("lineItems") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> lineItemsMap = (List<Map<String, Object>>) requestMap.get("lineItems");
                List<MaterialRequisitionItemEntity> lineItems = new ArrayList<>();

                for (Map<String, Object> itemMap : lineItemsMap) {
                    MaterialRequisitionItemEntity item = new MaterialRequisitionItemEntity();

                    if (itemMap.containsKey("uniqueCode")) {
                        item.setUniqueCode((String) itemMap.get("uniqueCode"));
                    }

                    if (itemMap.containsKey("productName")) {
                        item.setProductName((String) itemMap.get("productName"));
                    }

                    if (itemMap.containsKey("unitOfMeasure")) {
                        item.setUnitOfMeasure((String) itemMap.get("unitOfMeasure"));
                    }

                    if (itemMap.containsKey("quantity")) {
                        // Handle different numeric types
                        Object quantityObj = itemMap.get("quantity");
                        if (quantityObj instanceof Integer) {
                            item.setQuantity(((Integer) quantityObj).doubleValue());
                        } else if (quantityObj instanceof Double) {
                            item.setQuantity((Double) quantityObj);
                        } else if (quantityObj instanceof String) {
                            try {
                                item.setQuantity(Double.parseDouble((String) quantityObj));
                            } catch (NumberFormatException e) {
                                // Default to 0 if parsing fails
                                item.setQuantity(0.0);
                            }
                        }
                    }

                    if (itemMap.containsKey("remarks")) {
                        item.setRemarks((String) itemMap.get("remarks"));
                    }

                    if (itemMap.containsKey("attachments")) {
                        Object attachmentsObj = itemMap.get("attachments");
                        if (attachmentsObj instanceof String) {
                            item.setAttachments((String) attachmentsObj);
                        } else if (attachmentsObj instanceof List) {
                            item.setAttachments(new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(attachmentsObj));
                        }
                    }

                    item.setMaterialRequisition(requisition);
                    lineItems.add(item);
                }

                // Validate for duplicate line items before setting
                validateNoDuplicateMRLineItems(lineItems);
                requisition.setLineItems(lineItems);
            }

            MaterialRequisitionEntity savedRequisition = materialRequisitionRepository.save(requisition);

            // Create a Purchase Request from the Material Requisition
            purchaseRequestService.createPurchaseRequestFromMR(savedRequisition);

            // Create a response with success flag
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedRequisition);
            response.put("message", "Requisition created successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            // Handle validation errors (like duplicate line items) with 400 Bad Request
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            // Handle other unexpected errors with 500 Internal Server Error
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "Failed to create requisition: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/list")
    public ResponseEntity<?> getAllRequisitions(
    		 @RequestParam(defaultValue = "0") int page,
    	     @RequestParam(defaultValue = "10") int size) {

        try {
        	Pageable pageable = PageRequest.of(page, size);
        	Page<MaterialRequisitionEntity> requisitionsPage = materialRequisitionRepository.findAllByOrderByCreatedDateDesc(pageable);

            List<Map<String, Object>> responseData = new ArrayList<>();

            for (MaterialRequisitionEntity requisition : requisitionsPage.getContent()) {
                Map<String, Object> requisitionData = new HashMap<>();
                requisitionData.put("id", requisition.getId());
                requisitionData.put("requisitionId", requisition.getRequisitionId());
                requisitionData.put("yardNumber", requisition.getYardNumber());
                requisitionData.put("contractorName", requisition.getContractorName());
                requisitionData.put("projectName", requisition.getProjectName());
                requisitionData.put("createdDate", requisition.getCreatedDate());
                requisitionData.put("status", requisition.getStatus());
                requisitionData.put("lineItems", requisition.getLineItems());
                requisitionData.put("lineItemsCount", requisition.getLineItems().size());
                responseData.add(requisitionData);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", responseData);
            response.put("currentPage", requisitionsPage.getNumber());
            response.put("totalPages", requisitionsPage.getTotalPages());
            response.put("totalElements", requisitionsPage.getTotalElements());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", "Failed to fetch requisitions: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getRequisitionById(@PathVariable Long id) {
        try {
            Optional<MaterialRequisitionEntity> requisitionOpt = materialRequisitionRepository.findById(id);
            if (requisitionOpt.isPresent()) {
                MaterialRequisitionEntity requisition = requisitionOpt.get();
                Map<String, Object> requisitionData = new HashMap<>();
                requisitionData.put("id", requisition.getId());
                requisitionData.put("requisitionId", requisition.getRequisitionId());
                requisitionData.put("yardNumber", requisition.getYardNumber());
                requisitionData.put("contractorName", requisition.getContractorName());
                requisitionData.put("projectName", requisition.getProjectName());
                requisitionData.put("createdDate", requisition.getCreatedDate());
                requisitionData.put("status", requisition.getStatus());
                requisitionData.put("lineItems", requisition.getLineItems());
                requisitionData.put("lineItemsCount", requisition.getLineItems().size());

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", requisitionData);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Requisition not found with ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch requisition: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // Keep the old endpoint for backward compatibility, but mark it as deprecated
    @GetMapping("/byRequisitionId/{requisitionId}")
    @Deprecated
    public ResponseEntity<?> getRequisitionByRequisitionId(@PathVariable String requisitionId) {
        try {
            Optional<MaterialRequisitionEntity> requisitionOpt = materialRequisitionRepository.findByRequisitionId(requisitionId);
            if (requisitionOpt.isPresent()) {
                MaterialRequisitionEntity requisition = requisitionOpt.get();
                Map<String, Object> requisitionData = new HashMap<>();
                requisitionData.put("id", requisition.getId());
                requisitionData.put("requisitionId", requisition.getRequisitionId());
                requisitionData.put("yardNumber", requisition.getYardNumber());
                requisitionData.put("contractorName", requisition.getContractorName());
                requisitionData.put("projectName", requisition.getProjectName());
                requisitionData.put("createdDate", requisition.getCreatedDate());
                requisitionData.put("status", requisition.getStatus());
                requisitionData.put("lineItems", requisition.getLineItems());
                requisitionData.put("lineItemsCount", requisition.getLineItems().size());

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", requisitionData);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Requisition not found with ID: " + requisitionId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch requisition: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateRequisition(@PathVariable Long id, @RequestBody Map<String, Object> requestMap) {
        try {
            Optional<MaterialRequisitionEntity> existingRequisitionOpt = materialRequisitionRepository.findById(id);
            if (existingRequisitionOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Requisition not found with ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            MaterialRequisitionEntity existingRequisition = existingRequisitionOpt.get();

            // Store the original requisitionId to preserve it
            String originalRequisitionId = existingRequisition.getRequisitionId();

            // Update all editable fields
            if (requestMap.containsKey("status")) {
                existingRequisition.setStatus((String) requestMap.get("status"));
            }

            if (requestMap.containsKey("yardNumber")) {
                existingRequisition.setYardNumber((String) requestMap.get("yardNumber"));
            }

            if (requestMap.containsKey("contractorName")) {
                existingRequisition.setContractorName((String) requestMap.get("contractorName"));
            }

            if (requestMap.containsKey("projectName")) {
                existingRequisition.setProjectName((String) requestMap.get("projectName"));
            }

            if (requestMap.containsKey("priority")) {
                existingRequisition.setPriority((String) requestMap.get("priority"));
            }

            // Ensure we preserve the original requisitionId
            existingRequisition.setRequisitionId(originalRequisitionId);

            // Handle line items
            if (requestMap.containsKey("lineItems") && requestMap.get("lineItems") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> lineItemsMap = (List<Map<String, Object>>) requestMap.get("lineItems");
                List<MaterialRequisitionItemEntity> newLineItems = new ArrayList<>();

                // Process each item in the update request
                for (Map<String, Object> itemMap : lineItemsMap) {
                    // Check if this is an existing item (has ID)
                    if (itemMap.containsKey("id")) {
                        // Extract the ID value
                        Long extractedItemId = null;
                        Object idObj = itemMap.get("id");
                        if (idObj instanceof Integer) {
                            extractedItemId = ((Integer) idObj).longValue();
                        } else if (idObj instanceof Long) {
                            extractedItemId = (Long) idObj;
                        } else if (idObj instanceof String) {
                            try {
                                extractedItemId = Long.parseLong((String) idObj);
                            } catch (NumberFormatException e) {
                                // Invalid ID format, treat as new item
                                extractedItemId = null;
                            }
                        }

                        // Use the extracted ID if valid
                        if (extractedItemId != null) {
                            final Long itemId = extractedItemId; // Create a final variable for the lambda
                            // Find the existing item by ID
                            Optional<MaterialRequisitionItemEntity> existingItemOpt = existingRequisition.getLineItems().stream()
                                .filter(i -> i.getId().equals(itemId))
                                .findFirst();

                            if (existingItemOpt.isPresent()) {
                                // Update existing item
                                MaterialRequisitionItemEntity existingItem = existingItemOpt.get();

                                if (itemMap.containsKey("uniqueCode")) {
                                    existingItem.setUniqueCode((String) itemMap.get("uniqueCode"));
                                }

                                if (itemMap.containsKey("productName")) {
                                    existingItem.setProductName((String) itemMap.get("productName"));
                                }

                                if (itemMap.containsKey("unitOfMeasure")) {
                                    existingItem.setUnitOfMeasure((String) itemMap.get("unitOfMeasure"));
                                }

                                if (itemMap.containsKey("quantity")) {
                                    // Handle different numeric types
                                    Object quantityObj = itemMap.get("quantity");
                                    if (quantityObj instanceof Integer) {
                                        existingItem.setQuantity(((Integer) quantityObj).doubleValue());
                                    } else if (quantityObj instanceof Double) {
                                        existingItem.setQuantity((Double) quantityObj);
                                    } else if (quantityObj instanceof String) {
                                        try {
                                            existingItem.setQuantity(Double.parseDouble((String) quantityObj));
                                        } catch (NumberFormatException e) {
                                            // Keep existing value if parsing fails
                                        }
                                    }
                                }

                                if (itemMap.containsKey("remarks")) {
                                    existingItem.setRemarks((String) itemMap.get("remarks"));
                                }

                                if (itemMap.containsKey("attachments")) {
                                    Object attachmentsObj = itemMap.get("attachments");
                                    if (attachmentsObj instanceof String) {
                                        existingItem.setAttachments((String) attachmentsObj);
                                    } else if (attachmentsObj instanceof List) {
                                        existingItem.setAttachments(new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(attachmentsObj));
                                    }
                                }

                                newLineItems.add(existingItem);
                            } else {
                                // Item ID not found, create as new
                                MaterialRequisitionItemEntity newItem = createLineItemFromMap(itemMap);
                                newItem.setMaterialRequisition(existingRequisition);
                                newLineItems.add(newItem);
                            }
                        } else {
                            // Invalid ID, create as new
                            MaterialRequisitionItemEntity newItem = createLineItemFromMap(itemMap);
                            newItem.setMaterialRequisition(existingRequisition);
                            newLineItems.add(newItem);
                        }
                    } else {
                        // New item (no ID)
                        MaterialRequisitionItemEntity newItem = createLineItemFromMap(itemMap);
                        newItem.setMaterialRequisition(existingRequisition);
                        newLineItems.add(newItem);
                    }
                }

                // Validate for duplicate line items before updating
                validateNoDuplicateMRLineItems(newLineItems);

                // Clear and set the new list
                existingRequisition.getLineItems().clear();
                existingRequisition.getLineItems().addAll(newLineItems);
            }

            MaterialRequisitionEntity savedRequisition = materialRequisitionRepository.save(existingRequisition);

            // Sync the associated PR if it exists and is still pending vendor selection
            purchaseRequestService.syncPurchaseRequestFromMR(savedRequisition);

            // Create a response with success flag
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedRequisition);
            response.put("message", "Requisition updated successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            // Handle validation errors (like duplicate line items) with 400 Bad Request
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            // Handle other unexpected errors with 500 Internal Server Error
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "Error updating requisition: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // Helper method to create a line item from a map
    private MaterialRequisitionItemEntity createLineItemFromMap(Map<String, Object> itemMap) {
        MaterialRequisitionItemEntity item = new MaterialRequisitionItemEntity();

        if (itemMap.containsKey("uniqueCode")) {
            item.setUniqueCode((String) itemMap.get("uniqueCode"));
        }

        if (itemMap.containsKey("productName")) {
            item.setProductName((String) itemMap.get("productName"));
        }

        if (itemMap.containsKey("unitOfMeasure")) {
            item.setUnitOfMeasure((String) itemMap.get("unitOfMeasure"));
        }

        if (itemMap.containsKey("quantity")) {
            // Handle different numeric types
            Object quantityObj = itemMap.get("quantity");
            if (quantityObj instanceof Integer) {
                item.setQuantity(((Integer) quantityObj).doubleValue());
            } else if (quantityObj instanceof Double) {
                item.setQuantity((Double) quantityObj);
            } else if (quantityObj instanceof String) {
                try {
                    item.setQuantity(Double.parseDouble((String) quantityObj));
                } catch (NumberFormatException e) {
                    // Default to 0 if parsing fails
                    item.setQuantity(0.0);
                }
            }
        }

        if (itemMap.containsKey("remarks")) {
            item.setRemarks((String) itemMap.get("remarks"));
        }

        if (itemMap.containsKey("attachments")) {
            Object attachmentsObj = itemMap.get("attachments");
            try {
                if (attachmentsObj instanceof String) {
                    item.setAttachments((String) attachmentsObj);
                } else if (attachmentsObj instanceof List) {
                    item.setAttachments(new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(attachmentsObj));
                }
            } catch (Exception e) {
                // Handle JSON serialization error
            }
        }

        return item;
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteRequisition(@PathVariable Long id) {
        try {
            Optional<MaterialRequisitionEntity> existingRequisitionOpt = materialRequisitionRepository.findById(id);
            if (existingRequisitionOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ResponseDto.error("Requisition not found", 404));
            }
            materialRequisitionRepository.deleteById(id);
            return ResponseEntity.ok(ResponseDto.success(null, "Requisition deleted successfully"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ResponseDto.error("Error deleting requisition: " + e.getMessage(), 500));
        }
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<?> updateRequisitionStatus(@PathVariable Long id, @RequestParam String status) {
        try {
            Optional<MaterialRequisitionEntity> existingRequisitionOpt = materialRequisitionRepository.findById(id);
            if (existingRequisitionOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Requisition not found with ID: " + id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            MaterialRequisitionEntity existingRequisition = existingRequisitionOpt.get();
            existingRequisition.setStatus(status);
            MaterialRequisitionEntity savedRequisition = materialRequisitionRepository.save(existingRequisition);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedRequisition);
            response.put("message", "Status updated successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "Error updating status: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/export/pdf")
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<MaterialRequisitionEntity> requisitions = materialRequisitionRepository.findAllByOrderByCreatedDateDesc();
            exportService.exportAllToPdf(requisitions, response);
        } catch (DocumentException | IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<MaterialRequisitionEntity> requisitions = materialRequisitionRepository.findAllByOrderByCreatedDateDesc();
            exportService.exportAllToExcel(requisitions, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validates that there are no duplicate line items in the material requisition
     * Items are considered duplicates if they have the same uniqueCode or the same productName + unitOfMeasure combination
     */
    private void validateNoDuplicateMRLineItems(List<MaterialRequisitionItemEntity> lineItems) {
        if (lineItems == null || lineItems.isEmpty()) {
            return;
        }

        Set<String> seenItems = new HashSet<>();

        for (MaterialRequisitionItemEntity item : lineItems) {
            String itemKey = generateMRItemKey(item.getUniqueCode(), item.getProductName(), item.getUnitOfMeasure());

            if (seenItems.contains(itemKey)) {
                String itemDescription = item.getProductName() != null ? item.getProductName() :
                                       (item.getUniqueCode() != null ? item.getUniqueCode() : "Unknown item");
                throw new IllegalArgumentException(
                    "Duplicate line item detected: " + itemDescription +
                    ". Please remove duplicate items before submitting."
                );
            }
            seenItems.add(itemKey);
        }
    }

    /**
     * Generates a unique key for a material requisition item based on uniqueCode or productName + unitOfMeasure
     */
    private String generateMRItemKey(String uniqueCode, String productName, String unitOfMeasure) {
        // Primary key: uniqueCode (if available and not empty)
        if (uniqueCode != null && !uniqueCode.trim().isEmpty()) {
            return "CODE:" + uniqueCode.toLowerCase().trim();
        }

        // Fallback key: productName + unitOfMeasure combination
        String name = (productName != null ? productName.trim() : "");
        String unit = (unitOfMeasure != null ? unitOfMeasure.trim() : "");

        if (name.isEmpty()) {
            throw new IllegalArgumentException("Item must have either uniqueCode or productName");
        }

        return "NAME_UNIT:" + (name + "|" + unit).toLowerCase();
    }

    @GetMapping("/bulk-upload/template")
    @Operation(summary = "Download bulk upload template for line items", description = "Download Excel template for Material Requisition line items bulk upload with sample data including remarks and attachments")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Template downloaded successfully"),
            @ApiResponse(responseCode = "500", description = "Error generating template")
    })
    public void downloadLineItemsTemplate(HttpServletResponse response) {
        try {
            exportService.generateMRLineItemsTemplate(response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}