package com.synergy.controller;

import com.synergy.service.EmailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.*;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/email-test")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Email Testing", description = "APIs for testing email functionality")
public class EmailTestController {

    @Autowired
    private EmailService emailService;

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.properties.mail.smtp.from}")
    private String fromEmail;

    @PostMapping("/send-test")
    @Operation(summary = "Send test email", description = "Sends a test email to verify SMTP configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Email sent successfully"),
        @ApiResponse(responseCode = "500", description = "Failed to send email")
    })
    public ResponseEntity<?> sendTestEmail(@RequestBody Map<String, String> request) {
        try {
            String toEmail = request.get("email");
            String vendorName = request.get("name");

            if (toEmail == null || toEmail.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Email address is required");
                return ResponseEntity.badRequest().body(response);
            }

            if (vendorName == null || vendorName.isEmpty()) {
                vendorName = "Test Vendor";
            }

            // Use the existing method in EmailService to send a test email
            emailService.sendQuotationRequestEmail(
                toEmail,
                vendorName,
                999L, // Dummy PR ID
                "http://localhost:4200/test-link" // Dummy form URL
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Test email sent successfully to " + toEmail);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to send email: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/send-direct")
    @Operation(summary = "Send direct test email", description = "Sends a test email directly using JavaMailSender")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Email sent successfully"),
        @ApiResponse(responseCode = "500", description = "Failed to send email")
    })
    public ResponseEntity<?> sendDirectTestEmail(@RequestBody Map<String, String> request) {
        try {
            String toEmail = request.get("email");
            String subject = request.getOrDefault("subject", "Test Email from Synergy");
            String content = request.getOrDefault("content", "This is a test email from Synergy application.");

            if (toEmail == null || toEmail.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Email address is required");
                return ResponseEntity.badRequest().body(response);
            }

            // Create a simple mail message
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(content, true);
            helper.setFrom(fromEmail);

            // Send the message
            mailSender.send(message);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Direct test email sent successfully to " + toEmail);
            response.put("from", fromEmail);
            response.put("subject", subject);
            return ResponseEntity.ok(response);
        } catch (MessagingException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to send email: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
