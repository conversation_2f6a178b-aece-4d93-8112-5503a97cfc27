package com.synergy.controller;

import com.synergy.dto.VendorPropertyDTO;
import com.synergy.service.VendorPropertyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/vendor-properties")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Vendor Property Management", description = "APIs for managing vendor properties")
public class VendorPropertyController {

    @Autowired
    private VendorPropertyService vendorPropertyService;

    @GetMapping("/list")
    @Operation(summary = "Get all vendor properties", description = "Retrieves a list of all vendor properties (items and vendor types)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Vendor properties retrieved successfully")
    })
    public ResponseEntity<?> getAllVendorProperties() {
        try {
            List<VendorPropertyDTO> vendorProperties = vendorPropertyService.getAllVendorProperties();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", vendorProperties);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch vendor properties: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
