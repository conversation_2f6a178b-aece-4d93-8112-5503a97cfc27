package com.synergy.controller;

import com.itextpdf.text.DocumentException;
import com.synergy.dto.ContractorDTO;
import com.synergy.dto.PurchaseOrderDeliveryTermsDTO;
import com.synergy.entity.ContractorEntity;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.service.ContractorService;
import com.synergy.service.ExportService;
import com.synergy.repository.ContractorRepository;
import com.synergy.repository.PurchaseRequestRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/contractors")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Contractor Management", description = "APIs for managing contractors")
public class ContractorController {

    @Autowired
    private ContractorService contractorService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private ContractorRepository contractorRepository;

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @GetMapping("/list")
    @Operation(summary = "Get all contractors", description = "Retrieves a list of all contractors")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contractors retrieved successfully")
    })
    public ResponseEntity<?> getAllContractors() {
        try {
            List<ContractorDTO> contractors = contractorService.getAllContractors();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", contractors);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch contractors: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/add")
    @Operation(summary = "Add a new contractor", description = "Creates a new contractor in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contractor added successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<?> addContractor(@RequestBody ContractorDTO contractorDTO) {
        try {
            ContractorDTO savedContractor = contractorService.createContractor(contractorDTO);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Contractor added successfully");
            response.put("data", savedContractor);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (JsonProcessingException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error processing address data: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to add contractor: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/edit/{id}")
    @Operation(summary = "Update a contractor", description = "Updates an existing contractor by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contractor updated successfully"),
        @ApiResponse(responseCode = "404", description = "Contractor not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<?> editContractor(
            @Parameter(description = "ID of the contractor to update") @PathVariable Long id,
            @RequestBody ContractorDTO contractorDTO) {
        try {
            ContractorDTO updatedContractor = contractorService.updateContractor(id, contractorDTO);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Contractor updated successfully");
            response.put("data", updatedContractor);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (JsonProcessingException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error processing address data: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to update contractor: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a contractor", description = "Deletes a contractor by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contractor deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Contractor not found")
    })
    public ResponseEntity<?> deleteContractor(@PathVariable Long id) {
        try {
            contractorService.deleteContractor(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Contractor deleted successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete contractor: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/names")
    @Operation(summary = "Get all contractor names", description = "Retrieves a list of all contractor names for debugging")
    public ResponseEntity<?> getAllContractorNames() {
        try {
            List<ContractorDTO> contractors = contractorService.getAllContractors();
            Map<String, String> contractorNames = new HashMap<>();

            for (ContractorDTO contractor : contractors) {
                contractorNames.put(
                        String.valueOf(contractor.getSrNo()),
                        "contractorName: '" + contractor.getVendorName());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", contractorNames);
            response.put("count", contractorNames.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch contractor names: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/export/pdf")
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<ContractorEntity> contractors = contractorRepository.findAll();
            exportService.exportContractorsToPdf(contractors, response);
        } catch (DocumentException | IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<ContractorEntity> contractors = contractorRepository.findAll();
            exportService.exportContractorsToExcel(contractors, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate a Purchase Order PDF for an approved purchase request
     *
     * @param purchaseRequestId The ID of the purchase request
     * @param deliveryTerms The delivery terms from the form
     * @param response The HTTP response
     */
    @PostMapping("/purchase-order/generate-pdf/{purchaseRequestId}")
    @Operation(
        summary = "Generate Purchase Order PDF",
        description = "Generates a PDF purchase order for an approved purchase request with the provided delivery terms"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "PDF generated successfully"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in approved status")
    })
    public ResponseEntity<?> generatePurchaseOrderPdf(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long purchaseRequestId,
            @RequestBody PurchaseOrderDeliveryTermsDTO deliveryTerms,
            HttpServletResponse response) {
        try {
            // Find the purchase request
            PurchaseRequestEntity purchaseRequest = purchaseRequestRepository.findById(purchaseRequestId)
                    .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + purchaseRequestId));

            // Check if the purchase request is in approved status
            if (!"APPROVED".equals(purchaseRequest.getStatus())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "Purchase Request is not in APPROVED status");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Set the purchase request ID in the delivery terms
            deliveryTerms.setPurchaseRequestId(purchaseRequestId);

            // Generate the PDF
            exportService.generatePurchaseOrderPdf(purchaseRequest, deliveryTerms, response);

            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Failed to generate Purchase Order PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
