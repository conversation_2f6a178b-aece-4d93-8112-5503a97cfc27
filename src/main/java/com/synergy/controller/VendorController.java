package com.synergy.controller;

import com.itextpdf.text.DocumentException;
import com.synergy.dto.VendorDTO;
import com.synergy.entity.MaterialRequisitionEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.service.VendorService;
import com.synergy.service.ExportService;
import com.synergy.repository.VendorRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/vendors")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
	RequestMethod.DELETE, RequestMethod.OPTIONS })

@Tag(name = "Vendor Management", description = "APIs for managing vendors")
public class VendorController {

	@Autowired
	private VendorService vendorService;

	@Autowired
	private ExportService exportService;

	@Autowired
	private VendorRepository vendorRepository;

	@GetMapping("/list")
	@Operation(summary = "Get all vendors", description = "Retrieves a list of all vendors")
	@ApiResponses(value = { @ApiResponse(responseCode = "200", description = "Vendors retrieved successfully") })
	public ResponseEntity<?> getAllVendors() {
		try {
			List<VendorDTO> vendors = vendorService.getAllVendors();
			Map<String, Object> response = new HashMap<>();
			response.put("success", true);
			response.put("message", "Vendors retrieved successfully");
			response.put("data", vendors);
			return ResponseEntity.ok(response);
		} catch (Exception e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Failed to fetch vendors: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		}
	}

	@PostMapping("/add")
	@Operation(summary = "Add a new vendor", description = "Creates a new vendor in the system")
	@ApiResponses(value = { @ApiResponse(responseCode = "200", description = "Vendor added successfully"),
			@ApiResponse(responseCode = "400", description = "Invalid input") })
	public ResponseEntity<?> addVendor(@RequestBody VendorDTO vendorDTO) {
		try {
			VendorDTO savedVendor = vendorService.createVendor(vendorDTO);
			Map<String, Object> response = new HashMap<>();
			response.put("success", true);
			response.put("message", "Vendor added successfully");
			response.put("data", savedVendor);
			return ResponseEntity.ok(response);
		} catch (IllegalArgumentException e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", e.getMessage());
			return ResponseEntity.badRequest().body(response);
		} catch (JsonProcessingException e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Error processing address data: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		} catch (Exception e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Failed to add vendor: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		}
	}

	@GetMapping("/{id}")
	@Operation(summary = "Get vendor by ID", description = "Retrieves a vendor by their ID")
	@ApiResponses(value = {
		@ApiResponse(responseCode = "200", description = "Vendor retrieved successfully"),
		@ApiResponse(responseCode = "404", description = "Vendor not found")
	})
	public ResponseEntity<?> getVendorById(@Parameter(description = "ID of the vendor to retrieve") @PathVariable Long id) {
		try {
			VendorDTO vendor = vendorService.getVendorById(id);
			Map<String, Object> response = new HashMap<>();
			response.put("success", true);
			response.put("message", "Vendor retrieved successfully");
			response.put("data", vendor);
			return ResponseEntity.ok(response);
		} catch (IllegalArgumentException e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", e.getMessage());
			return ResponseEntity.status(404).body(response);
		} catch (Exception e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Error retrieving vendor: " + e.getMessage());
			return ResponseEntity.status(500).body(response);
		}
	}

	@PutMapping("/edit/{id}")
	@Operation(summary = "Update a vendor", description = "Updates an existing vendor by ID")
	@ApiResponses(value = { @ApiResponse(responseCode = "200", description = "Vendor updated successfully"),
			@ApiResponse(responseCode = "404", description = "Vendor not found"),
			@ApiResponse(responseCode = "400", description = "Invalid input") })
	public ResponseEntity<?> editVendor(@Parameter(description = "ID of the vendor to update") @PathVariable Long id,
			@RequestBody VendorDTO vendorDTO) {
		try {
			VendorDTO updatedVendor = vendorService.updateVendor(id, vendorDTO);
			Map<String, Object> response = new HashMap<>();
			response.put("success", true);
			response.put("message", "Vendor updated successfully");
			response.put("data", updatedVendor);
			return ResponseEntity.ok(response);
		} catch (IllegalArgumentException e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", e.getMessage());
			return ResponseEntity.badRequest().body(response);
		} catch (JsonProcessingException e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Error processing address data: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		} catch (Exception e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Failed to update vendor: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		}
	}

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a vendor", description = "Deletes a vendor by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Vendor deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Vendor not found")
    })
    public ResponseEntity<?> deleteVendor(@PathVariable Long id) {
        try {
            vendorService.deleteVendor(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Vendor deleted successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete vendor: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/search/name/{name}")
    @Operation(summary = "Search vendors by name", description = "Searches for vendors by vendor name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    public ResponseEntity<?> searchVendorsByName(@PathVariable String name) {
        try {
            List<VendorDTO> vendors = vendorService.getAllVendors();
            List<VendorDTO> matchingVendors = vendors.stream()
                .filter(vendor -> vendor.getVendorName() != null &&
                                 vendor.getVendorName().toLowerCase().contains(name.toLowerCase()))
                .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", matchingVendors);
            response.put("count", matchingVendors.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to search vendors: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/names")
    @Operation(summary = "Get all vendor names", description = "Retrieves a list of all vendor names for debugging")
    public ResponseEntity<?> getAllVendorNames() {
        try {
            List<VendorDTO> vendors = vendorService.getAllVendors();
            Map<String, String> vendorNames = new HashMap<>();

            for (VendorDTO vendor : vendors) {
                vendorNames.put(String.valueOf(vendor.getSrNo()),
                               "companyName: '" + vendor.getCompanyName() + "', vendorName: '" + vendor.getVendorName() + "'");
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", vendorNames);
            response.put("count", vendorNames.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch vendor names: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

	@GetMapping("/export/pdf")
	public void exportAllToPdf(HttpServletResponse response) {
		try {
			List<VendorEntity> vendors = vendorRepository.findAll();
			exportService.exportVendorsToPdf(vendors, response);
		} catch (DocumentException | IOException e) {
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/export/excel")
	public void exportAllToExcel(HttpServletResponse response) {
		try {
			List<VendorEntity> vendors = vendorRepository.findAll();
			exportService.exportVendorsToExcel(vendors, response);
		} catch (Exception e) {
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/bulk-upload/template")
	@Operation(summary = "Download bulk upload template", description = "Download Excel template for vendor bulk upload with sample data")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Template downloaded successfully"),
			@ApiResponse(responseCode = "500", description = "Error generating template")
	})
	public void downloadBulkUploadTemplate(HttpServletResponse response) {
		try {
			vendorService.generateBulkUploadTemplate(response);
		} catch (Exception e) {
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/bulk-upload")
	@Operation(summary = "Bulk upload vendors from Excel", description = "Upload multiple vendors from an Excel file")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Bulk upload completed successfully"),
			@ApiResponse(responseCode = "400", description = "Invalid file or file processing error"),
			@ApiResponse(responseCode = "500", description = "Internal server error")
	})
	public ResponseEntity<?> bulkUploadVendors(@RequestParam("file") MultipartFile file) {
		try {
			// Validate file
			if (file.isEmpty()) {
				Map<String, Object> response = new HashMap<>();
				response.put("success", false);
				response.put("message", "Please select a file to upload");
				return ResponseEntity.badRequest().body(response);
			}

			// Check file type
			String fileName = file.getOriginalFilename();
			if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
				Map<String, Object> response = new HashMap<>();
				response.put("success", false);
				response.put("message", "Please upload a valid Excel file (.xlsx or .xls)");
				return ResponseEntity.badRequest().body(response);
			}

			// Process the file
			Map<String, Object> result = vendorService.bulkUploadVendors(file);

			Map<String, Object> response = new HashMap<>();
			response.put("success", true);
			response.put("message", "Bulk upload completed");
			response.put("data", result);

			return ResponseEntity.ok(response);

		} catch (Exception e) {
			Map<String, Object> response = new HashMap<>();
			response.put("success", false);
			response.put("message", "Error processing file: " + e.getMessage());
			return ResponseEntity.status(500).body(response);
		}
	}
}