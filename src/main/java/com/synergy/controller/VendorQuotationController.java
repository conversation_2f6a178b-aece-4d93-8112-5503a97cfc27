package com.synergy.controller;

import com.synergy.dto.VendorQuotationDTO;
import com.synergy.dto.VendorQuotationSelectionDTO;
import com.synergy.entity.VendorQuotationEntity;
import com.synergy.service.VendorQuotationService;
import com.synergy.service.ExportService;
import com.synergy.repository.VendorQuotationRepository;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/vendor-quotations")
public class VendorQuotationController {

    private static final Logger logger = LoggerFactory.getLogger(VendorQuotationController.class);

    @Autowired
    private VendorQuotationService vendorQuotationService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;

    @PostMapping
    @Operation(summary = "Submit vendor quotation", description = "Submit a vendor quotation for a purchase request. Requires a valid bidding token for one-time access.")
    public ResponseEntity<?> submitQuotation(
            @RequestBody VendorQuotationDTO dto,
            @Parameter(description = "Bidding token for one-time access", required = false) @RequestParam(required = false) String token) {
        try {
            logger.info("Submitting quotation for PR {} from vendor {} with token {}",
                    dto.getPurchaseRequestId(), dto.getVendorId(), token != null ? "provided" : "not provided");

            VendorQuotationDTO savedQuotation = vendorQuotationService.submitQuotation(dto, token);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedQuotation);
            response.put("message", "Quotation submitted successfully. This bidding link is now invalid.");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid quotation submission", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (SecurityException e) {
            logger.error("Security error in quotation submission", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(403).body(response);
        } catch (Exception e) {
            logger.error("Error submitting quotation", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "An error occurred while submitting the quotation. Please try again.");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/{quotationId}/select")
    public ResponseEntity<?> selectQuotation(@PathVariable Long quotationId) {
        try {
            VendorQuotationDTO selectedQuotation = vendorQuotationService.selectQuotation(quotationId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", selectedQuotation);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error selecting quotation", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/pr/{prId}/select-items")
    @Operation(summary = "Select items from multiple vendors for a PR", description = "Select items from different vendors for a purchase request. Only one item per uniqueCode can be selected.")
    public ResponseEntity<?> selectItemsForPR(
            @PathVariable Long prId,
            @RequestBody VendorQuotationSelectionDTO selectionDTO) {
        try {
            logger.info("Selecting items for PR {}: {}", prId, selectionDTO);
            List<VendorQuotationDTO> updatedQuotations = vendorQuotationService.selectItemsForPR(prId, selectionDTO);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedQuotations);
            response.put("count", updatedQuotations.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error selecting items for PR {}", prId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{quotationId}/select-items")
    @Deprecated
    public ResponseEntity<?> selectQuotationItems(
            @PathVariable Long quotationId,
            @RequestBody VendorQuotationSelectionDTO selectionDTO) {
        try {
            logger.info("Selecting items for quotation {}: {}", quotationId, selectionDTO);
            VendorQuotationDTO updatedQuotation = vendorQuotationService.selectQuotationItems(
                    quotationId,
                    selectionDTO.getVendorSelections() != null ? selectionDTO.getVendorSelections().get(quotationId)
                            : null,
                    null);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedQuotation);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error selecting quotation items", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/pr/{prId}")
    public ResponseEntity<?> getQuotationsForPR(@PathVariable Long prId) {
        try {
            // Add validation
            if (prId == null || prId <= 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Invalid Purchase Request ID");
                return ResponseEntity.badRequest().body(response);
            }

            logger.info("Getting quotations for PR {}", prId);
            List<VendorQuotationDTO> quotations = vendorQuotationService.getQuotationsForPR(prId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", quotations);
            response.put("count", quotations.size());
            response.put("message", quotations.isEmpty() ? "No quotations found" : "Quotations retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error getting quotations for PR {}", prId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch quotations: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/pr/{prId}/selected-items")
    public ResponseEntity<?> getSelectedItemsForPR(@PathVariable Long prId) {
        try {
            List<VendorQuotationDTO> selectedQuotations = vendorQuotationService.getSelectedItemsForPR(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", selectedQuotations);
            response.put("count", selectedQuotations.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error fetching selected quotation items for PR {}", prId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/export/pdf")
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<VendorQuotationEntity> quotations = vendorQuotationRepository.findAll();
            exportService.exportVendorQuotationsToPdf(quotations, response);
        } catch (Exception e) {
            logger.error("Error exporting vendor quotations to PDF", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<VendorQuotationEntity> quotations = vendorQuotationRepository.findAll();
            exportService.exportVendorQuotationsToExcel(quotations, response);
        } catch (Exception e) {
            logger.error("Error exporting vendor quotations to Excel", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/validate-token")
    @Operation(summary = "Validate bidding token and get PR details", description = "Validates a bidding token and returns purchase request details for the bidding form. "
            +
            "This endpoint is used by the frontend to check if a bidding link is valid and get the necessary data.")
    public ResponseEntity<?> validateTokenAndGetPRDetails(
            @Parameter(description = "Bidding token to validate", required = true) @RequestParam String token) {
        try {
            logger.info("Validating bidding token: {}", token);

            Map<String, Object> result = vendorQuotationService.validateTokenAndGetPRDetails(token);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (SecurityException e) {
            logger.error("Invalid or expired token: {}", token, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(403).body(response);
        } catch (Exception e) {
            logger.error("Error validating token: {}", token, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "An error occurred while validating the token.");
            return ResponseEntity.status(500).body(response);
        }
    }

}
