package com.synergy.controller;

import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.repository.ShipbuildersItemRepository;
import com.synergy.service.FileStorageService;
import com.synergy.service.ExportService;
import com.synergy.service.ShipbuildersItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/items")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
    RequestMethod.DELETE, RequestMethod.OPTIONS})

@Tag(name = "Item Management", description = "APIs for managing shipbuilders items")
public class ShipbuildersItemController {

    private static final Logger logger = LoggerFactory.getLogger(ShipbuildersItemController.class);

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Autowired
    private com.synergy.service.ShipbuildersItemService shipbuildersItemService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ExportService exportService;

    @PostMapping("/add")
    @Operation(summary = "Add a new item", description = "Creates a new item in the system with optional base64 image")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Item added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<?> addItem(@RequestBody Map<String, Object> requestData) {
        try {
            // Extract item data
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            ShipbuildersItemEntity item = mapper.convertValue(requestData.get("item"), ShipbuildersItemEntity.class);

            // Process base64 image if provided
            String base64Image = (String) requestData.get("image");
            if (base64Image != null && !base64Image.isEmpty()) {
                // Extract image data from base64 string
                String[] parts = base64Image.split(",");
                String imageType = "png"; // Default to png

                // Try to extract the image type from the base64 string
                if (parts.length > 1 && parts[0].contains(":") && parts[0].contains(";")) {
                    imageType = parts[0].split(":")[1].split(";")[0].split("/")[1];
                }

                // Decode the base64 string
                byte[] imageBytes;
                if (parts.length > 1) {
                    imageBytes = Base64.getDecoder().decode(parts[1]);
                } else {
                    imageBytes = Base64.getDecoder().decode(base64Image);
                }

                // Generate a unique filename
                String fileName = fileStorageService.generateUniqueFilename("image", imageType);
                Path uploadPath = fileStorageService.getUploadPath();

                // Create directory if it doesn't exist
                if (!Files.exists(uploadPath)) {
                    Files.createDirectories(uploadPath);
                }

                // Save the file
                Path filePath = uploadPath.resolve(fileName);
                Files.write(filePath, imageBytes);

                // Set the image path in the item entity
                item.setImagePath(fileName);
            }

            // Save the item
            ShipbuildersItemEntity savedItem = shipbuildersItemService.saveItem(item);

            // Create response with image URL
            Map<String, Object> itemData = new HashMap<>();
            itemData.put("id", savedItem.getId());
            itemData.put("itemName", savedItem.getItemName());
            itemData.put("category", savedItem.getCategory());
            itemData.put("subCategory", savedItem.getSubCategory());
            itemData.put("qualityCheck", savedItem.getQualityCheck());
            itemData.put("itemCode", savedItem.getItemCode());
            itemData.put("specification1", savedItem.getSpecification1());
            itemData.put("dropdown", savedItem.getDropdown());
            itemData.put("specification2", savedItem.getSpecification2());
            itemData.put("specification3", savedItem.getSpecification3());
            itemData.put("materialDescription", savedItem.getMaterialDescription());
            itemData.put("material", savedItem.getMaterial());
            itemData.put("purchasingUOM", savedItem.getPurchasingUOM());
            itemData.put("inventoryUOM", savedItem.getInventoryUOM());
            itemData.put("qtyPerPack", savedItem.getQtyPerPack());
            itemData.put("reorderLevel", savedItem.getReorderLevel());
            itemData.put("lastRate", savedItem.getLastRate());
            itemData.put("gstSlab", savedItem.getGstSlab());
            itemData.put("unitOfMeasure", savedItem.getUnitOfMeasure());
            itemData.put("description", savedItem.getDescription());
            itemData.put("imagePath", savedItem.getImagePath());

            // Add the full image URL if an image exists
            if (savedItem.getImagePath() != null && !savedItem.getImagePath().isEmpty()) {
                itemData.put("imageUrl", fileStorageService.getImageUrl(savedItem.getImagePath()));
            } else {
                itemData.put("imageUrl", null);
            }

            // Return success response
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Item added successfully");
            response.put("data", itemData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to add item: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/edit/{id}")
    @Operation(summary = "Update an item", description = "Updates an existing item by ID")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Item updated successfully"),
            @ApiResponse(responseCode = "404", description = "Item not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<?> editItem(@Parameter(description = "ID of the item to update") @PathVariable Long id,
                                      @RequestBody Map<String, Object> requestData) {
        try {
            Optional<ShipbuildersItemEntity> existingItemOpt = shipbuildersItemService.getItemById(id);
            if (existingItemOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Item not found with id: " + id);
                return ResponseEntity.badRequest().body(response);
            }

            ShipbuildersItemEntity existingItem = existingItemOpt.get();

            existingItem.setItemName((String) requestData.getOrDefault("itemName", existingItem.getItemName()));
            existingItem.setCategory((String) requestData.getOrDefault("category", existingItem.getCategory()));
            existingItem
                    .setSubCategory((String) requestData.getOrDefault("subCategory", existingItem.getSubCategory()));
            existingItem.setItemCode((String) requestData.getOrDefault("itemCode", existingItem.getItemCode()));
            existingItem.setSpecification1(
                    (String) requestData.getOrDefault("specification1", existingItem.getSpecification1()));
            existingItem.setSpecification2(
                    (String) requestData.getOrDefault("specification2", existingItem.getSpecification2()));
            existingItem.setSpecification3(
                    (String) requestData.getOrDefault("specification3", existingItem.getSpecification3()));
            existingItem.setDropdown((String) requestData.getOrDefault("dropdown", existingItem.getDropdown()));
            existingItem.setMaterialDescription(
                    (String) requestData.getOrDefault("materialDescription", existingItem.getMaterialDescription()));
            existingItem.setMaterial((String) requestData.getOrDefault("material", existingItem.getMaterial()));
            existingItem.setUnitOfMeasure(
                    (String) requestData.getOrDefault("unitOfMeasure", existingItem.getUnitOfMeasure()));
            existingItem
                    .setDescription((String) requestData.getOrDefault("description", existingItem.getDescription()));

            Object qualityCheck = requestData.get("qualityCheck");
                existingItem.setQualityCheck(((String) qualityCheck));

            Object purchasingUOM = requestData.get("purchasingUOM");
            existingItem.setPurchasingUOM((String) purchasingUOM);

            Object inventoryUOM = requestData.get("inventoryUOM");
            existingItem.setInventoryUOM((String) inventoryUOM);

            Object qtyPerPack = requestData.get("qtyPerPack");
            if (qtyPerPack instanceof Number)
                existingItem.setQtyPerPack(((Number) qtyPerPack).floatValue());

            Object reorderLevel = requestData.get("reorderLevel");
            if (reorderLevel instanceof Number)
                existingItem.setReorderLevel(((Number) reorderLevel).floatValue());

            Object lastRate = requestData.get("lastRate");
            if (lastRate instanceof Number)
                existingItem.setLastRate(((Number) lastRate).floatValue());

            Object gstSlab = requestData.get("gstSlab");
            if (gstSlab instanceof Number)
                existingItem.setGstSlab(((Number) gstSlab).floatValue());

            String base64Image = (String) requestData.get("image");
            if (base64Image != null && !base64Image.isEmpty()) {
                String[] parts = base64Image.split(",");
                String imageType = "png";
                if (parts.length > 1 && parts[0].contains(":") && parts[0].contains(";")) {
                    imageType = parts[0].split(":")[1].split(";")[0].split("/")[1];
                }
                byte[] imageBytes = parts.length > 1 ? Base64.getDecoder().decode(parts[1])
                        : Base64.getDecoder().decode(base64Image);
                String fileName = fileStorageService.generateUniqueFilename("image", imageType);
                Path uploadPath = fileStorageService.getUploadPath();
                if (!Files.exists(uploadPath))
                    Files.createDirectories(uploadPath);
                Path filePath = uploadPath.resolve(fileName);
                Files.write(filePath, imageBytes);
                existingItem.setImagePath(fileName);
            }

            ShipbuildersItemEntity updatedItem = shipbuildersItemService.saveItem(existingItem);
            shipbuildersItemService.clearItemCache(id);

            Map<String, Object> itemData = new HashMap<>();
            itemData.put("id", updatedItem.getId());
            itemData.put("itemName", updatedItem.getItemName());
            itemData.put("category", updatedItem.getCategory());
            itemData.put("subCategory", updatedItem.getSubCategory());
            itemData.put("qualityCheck", updatedItem.getQualityCheck());
            itemData.put("itemCode", updatedItem.getItemCode());
            itemData.put("specification1", updatedItem.getSpecification1());
            itemData.put("dropdown", updatedItem.getDropdown());
            itemData.put("specification2", updatedItem.getSpecification2());
            itemData.put("specification3", updatedItem.getSpecification3());
            itemData.put("materialDescription", updatedItem.getMaterialDescription());
            itemData.put("material", updatedItem.getMaterial());
            itemData.put("purchasingUOM", updatedItem.getPurchasingUOM());
            itemData.put("inventoryUOM", updatedItem.getInventoryUOM());
            itemData.put("qtyPerPack", updatedItem.getQtyPerPack());
            itemData.put("reorderLevel", updatedItem.getReorderLevel());
            itemData.put("lastRate", updatedItem.getLastRate());
            itemData.put("gstSlab", updatedItem.getGstSlab());
            itemData.put("unitOfMeasure", updatedItem.getUnitOfMeasure());
            itemData.put("description", updatedItem.getDescription());
            itemData.put("imagePath", updatedItem.getImagePath());
            itemData.put("imageUrl",
                    updatedItem.getImagePath() != null ? fileStorageService.getImageUrl(updatedItem.getImagePath())
                            : null);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Item updated successfully");
            response.put("data", itemData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to update item: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "Get item by ID", description = "Retrieves an item by its ID")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Item found"),
            @ApiResponse(responseCode = "404", description = "Item not found")})
    public ResponseEntity<?> getItemById(@Parameter(description = "ID of the item to retrieve") @PathVariable Long id) {
        try {
            logger.info("Fetching item with ID: {}", id);

            // Use the optimized service with caching
            Optional<ShipbuildersItemEntity> item = shipbuildersItemService.getItemById(id);
            if (item.isEmpty()) {
                logger.warn("Item not found with ID: {}", id);
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Item not found with id: " + id);
                return ResponseEntity.badRequest().body(response);
            }

            ShipbuildersItemEntity itemEntity = item.get();
            logger.info("Retrieved item with name: {}", itemEntity.getItemName());

            // Create response with image URL
            Map<String, Object> itemData = new HashMap<>();
            itemData.put("id", itemEntity.getId());
            itemData.put("itemName", itemEntity.getItemName());
            itemData.put("category", itemEntity.getCategory());
            itemData.put("subCategory", itemEntity.getSubCategory());
            itemData.put("qualityCheck", itemEntity.getQualityCheck());
            itemData.put("itemCode", itemEntity.getItemCode());
            itemData.put("specification1", itemEntity.getSpecification1());
            itemData.put("dropdown", itemEntity.getDropdown());
            itemData.put("specification2", itemEntity.getSpecification2());
            itemData.put("specification3", itemEntity.getSpecification3());
            itemData.put("materialDescription", itemEntity.getMaterialDescription());
            itemData.put("material", itemEntity.getMaterial());
            itemData.put("purchasingUOM", itemEntity.getPurchasingUOM());
            itemData.put("inventoryUOM", itemEntity.getInventoryUOM());
            itemData.put("qtyPerPack", itemEntity.getQtyPerPack());
            itemData.put("reorderLevel", itemEntity.getReorderLevel());
            itemData.put("lastRate", itemEntity.getLastRate());
            itemData.put("gstSlab", itemEntity.getGstSlab());
            itemData.put("unitOfMeasure", itemEntity.getUnitOfMeasure());
            itemData.put("description", itemEntity.getDescription());
            itemData.put("imagePath", itemEntity.getImagePath());

            // Add the full image URL if an image exists
            if (itemEntity.getImagePath() != null && !itemEntity.getImagePath().isEmpty()) {
                itemData.put("imageUrl", fileStorageService.getImageUrl(itemEntity.getImagePath()));
            } else {
                itemData.put("imageUrl", null);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", itemData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch item: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/by-code/{uniqueCode}")
    @Operation(summary = "Get item by unique code", description = "Retrieves an item by its unique code with all information and images")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Item found"),
            @ApiResponse(responseCode = "404", description = "Item not found")})
    public ResponseEntity<?> getItemByUniqueCode(@Parameter(description = "Unique code of the item") @PathVariable String uniqueCode) {
        try {
            Optional<ShipbuildersItemEntity> itemOpt = shipbuildersItemRepository.findByItemCode(uniqueCode);
            if (itemOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Item not found with unique code: " + uniqueCode);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            ShipbuildersItemEntity item = itemOpt.get();
            Map<String, Object> itemData = new HashMap<>();
            itemData.put("id", item.getId());
            itemData.put("itemName", item.getItemName());
            itemData.put("category", item.getCategory());
            itemData.put("subCategory", item.getSubCategory());
            itemData.put("qualityCheck", item.getQualityCheck());
            itemData.put("itemCode", item.getItemCode());
            itemData.put("specification1", item.getSpecification1());
            itemData.put("dropdown", item.getDropdown());
            itemData.put("specification2", item.getSpecification2());
            itemData.put("specification3", item.getSpecification3());
            itemData.put("materialDescription", item.getMaterialDescription());
            itemData.put("material", item.getMaterial());
            itemData.put("purchasingUOM", item.getPurchasingUOM());
            itemData.put("inventoryUOM", item.getInventoryUOM());
            itemData.put("qtyPerPack", item.getQtyPerPack());
            itemData.put("reorderLevel", item.getReorderLevel());
            itemData.put("lastRate", item.getLastRate());
            itemData.put("gstSlab", item.getGstSlab());
            itemData.put("unitOfMeasure", item.getUnitOfMeasure());
            itemData.put("description", item.getDescription());
            itemData.put("imagePath", item.getImagePath());

            if (item.getImagePath() != null && !item.getImagePath().isEmpty()) {
                itemData.put("imageUrl", fileStorageService.getImageUrl(item.getImagePath()));
            } else {
                itemData.put("imageUrl", null);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", itemData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch item: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/list")
    @Operation(summary = "Get all items", description = "Retrieves a list of all items with complete details")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Items retrieved successfully")})
    public ResponseEntity<?> getAllItems() {
        try {
            // Use the service with caching to get full item details
            List<ShipbuildersItemEntity> items = shipbuildersItemService.getAllItems();

            // Create a list to hold items with image URLs
            List<Map<String, Object>> itemsWithUrls = new ArrayList<>();

            // Add image URLs to each item
            for (ShipbuildersItemEntity item : items) {
                Map<String, Object> itemData = new HashMap<>();
                itemData.put("id", item.getId());
                itemData.put("itemName", item.getItemName());
                itemData.put("category", item.getCategory());
                itemData.put("subCategory", item.getSubCategory());
                itemData.put("qualityCheck", item.getQualityCheck());
                itemData.put("itemCode", item.getItemCode());
                itemData.put("specification1", item.getSpecification1());
                itemData.put("dropdown", item.getDropdown());
                itemData.put("specification2", item.getSpecification2());
                itemData.put("specification3", item.getSpecification3());
                itemData.put("materialDescription", item.getMaterialDescription());
                itemData.put("material", item.getMaterial());
                itemData.put("purchasingUOM", item.getPurchasingUOM());
                itemData.put("inventoryUOM", item.getInventoryUOM());
                itemData.put("qtyPerPack", item.getQtyPerPack());
                itemData.put("reorderLevel", item.getReorderLevel());
                itemData.put("lastRate", item.getLastRate());
                itemData.put("gstSlab", item.getGstSlab());
                itemData.put("unitOfMeasure", item.getUnitOfMeasure());
                itemData.put("description", item.getDescription());
                itemData.put("imagePath", item.getImagePath());

                // Add the full image URL if an image exists
                if (item.getImagePath() != null && !item.getImagePath().isEmpty()) {
                    itemData.put("imageUrl", fileStorageService.getImageUrl(item.getImagePath()));
                } else {
                    itemData.put("imageUrl", null);
                }

                itemsWithUrls.add(itemData);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", itemsWithUrls);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch items: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/dropdown")
    @Operation(summary = "Get items for dropdown", description = "Retrieves a lightweight list of items for dropdown menus")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Items retrieved successfully")})
    public ResponseEntity<?> getItemsForDropdown() {
        try {
            // Use the optimized service with caching and lightweight DTOs
            List<com.synergy.dto.ShipbuildersItemDropdownDTO> items = shipbuildersItemService.getAllItemsForDropdown();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", items);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch dropdown items: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/search")
    @Operation(summary = "Search items", description = "Searches items by various criteria")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Search results retrieved successfully")})
    public ResponseEntity<?> searchItems(
            @Parameter(description = "Subcategory to search for") @RequestParam(required = false) String subCategory,
            @Parameter(description = "Description to search for") @RequestParam(required = false) String description,
            @Parameter(description = "Item code to search for") @RequestParam(required = false) String itemCode) {
        try {
            List<ShipbuildersItemEntity> items = shipbuildersItemRepository.searchItems(subCategory, description,
                    itemCode);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", items);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to search items: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/subcategories")
    @Operation(summary = "Get all subcategories", description = "Retrieves a list of all unique subcategories")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Subcategories retrieved successfully")})
    public ResponseEntity<?> getAllSubCategories() {
        try {
            List<String> subCategories = shipbuildersItemRepository.findAllSubCategories();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", subCategories);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch subcategories: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteItem(@PathVariable Long id) {
        try {
            Optional<ShipbuildersItemEntity> existingItem = shipbuildersItemRepository.findById(id);
            if (existingItem.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Item not found with id: " + id);
                return ResponseEntity.badRequest().body(response);
            }

            shipbuildersItemRepository.deleteById(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Item deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete item: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/export/pdf")
    @Operation(summary = "Export items to PDF", description = "Exports all items to a PDF file")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "PDF export successful"),
            @ApiResponse(responseCode = "500", description = "Export failed")})
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<ShipbuildersItemEntity> items = shipbuildersItemRepository.findAll();
            exportService.exportShipbuildersItemsToPdf(items, response);
        } catch (Exception e) {
            logger.error("Error exporting items to PDF", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    @Operation(summary = "Export items to Excel", description = "Exports all items to an Excel file")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "Excel export successful"),
            @ApiResponse(responseCode = "500", description = "Export failed")})
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<ShipbuildersItemEntity> items = shipbuildersItemRepository.findAll();
            exportService.exportShipbuildersItemsToExcel(items, response);
        } catch (Exception e) {
            logger.error("Error exporting items to Excel", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bulk-upload/template")
    @Operation(summary = "Download bulk upload template", description = "Download Excel template for bulk upload with sample data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Template downloaded successfully"),
            @ApiResponse(responseCode = "500", description = "Error generating template")
    })
    public void downloadBulkUploadTemplate(HttpServletResponse response) {
        try {
            shipbuildersItemService.generateBulkUploadTemplate(response);
            logger.info("Bulk upload template downloaded successfully");
        } catch (Exception e) {
            logger.error("Error generating bulk upload template", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/bulk-upload")
    @Operation(summary = "Bulk upload items from Excel", description = "Upload multiple items from an Excel file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Bulk upload completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid file or file processing error"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> bulkUploadItems(@RequestParam("file") MultipartFile file) {
        try {
            // Validate file
            if (file.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Please select a file to upload");
                return ResponseEntity.badRequest().body(response);
            }

            // Check file type
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Please upload a valid Excel file (.xlsx or .xls)");
                return ResponseEntity.badRequest().body(response);
            }

            // Process the file
            Map<String, Object> result = shipbuildersItemService.bulkUploadItems(file);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Bulk upload completed");
            response.put("data", result);

            logger.info("Bulk upload completed for file: {}", fileName);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error during bulk upload", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error processing file: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}