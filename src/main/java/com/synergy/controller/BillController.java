package com.synergy.controller;

import com.synergy.dto.BillDTO;
import com.synergy.service.BillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/bills")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Bill Management", description = "APIs for managing bills created from GRNs")
public class BillController {

    @Autowired
    private BillService billService;

    /**
     * Get all bills
     */
    @GetMapping
    @Operation(
        summary = "Get all bills",
        description = "Retrieves a list of all bills ordered by creation date (newest first)",
        tags = {"Bill Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Bills retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getAllBills() {
        try {
            List<BillDTO> bills = billService.getAllBills();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", bills);
            response.put("message", "Bills retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve bills: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get bills by status
     */
    @GetMapping("/status/{status}")
    @Operation(
        summary = "Get bills by status",
        description = "Retrieves bills filtered by status (DRAFT, PENDING, APPROVED, PAID)",
        tags = {"Bill Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Bills retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getBillsByStatus(
            @Parameter(description = "Bill status", required = true, example = "DRAFT")
            @PathVariable String status) {
        try {
            List<BillDTO> bills = billService.getBillsByStatus(status);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", bills);
            response.put("message", "Bills retrieved successfully for status: " + status);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve bills: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get bill by ID
     */
    @GetMapping("/{id}")
    @Operation(
        summary = "Get bill by ID",
        description = "Retrieves a specific bill by its ID",
        tags = {"Bill Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Bill retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Bill not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getBillById(
            @Parameter(description = "Bill ID", required = true, example = "1")
            @PathVariable Long id) {
        try {
            BillDTO bill = billService.getBillById(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", bill);
            response.put("message", "Bill retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve bill: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
