package com.synergy.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Synergy Shipbuilders API")
                        .version("1.0")
                        .description("API documentation for Synergy Shipbuilders Management System")
                        .contact(new Contact()
                                .name("Synergy Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("http://springdoc.org")))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .schemaRequirement("bearerAuth", new SecurityScheme()
                        .name("bearerAuth")
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT"));
    }

    @Bean
    public GroupedOpenApi purchaseRequestApi() {
        return GroupedOpenApi.builder()
                .group("Purchase Request APIs")
                .pathsToMatch("/api/purchase-requests/**")
                .build();
    }

    @Bean
    public GroupedOpenApi purchaseRequestWorkflowApi() {
        return GroupedOpenApi.builder()
                .group("Purchase Request Workflow APIs")
                .pathsToMatch(
                    "/api/purchase-requests/for-internal",
                    "/api/purchase-requests/for-in-progress-management",
                    "/api/purchase-requests/*/send-back-to-vendors",
                    "/api/purchase-requests/*/approve-reject"
                )
                .build();
    }

    @Bean
    public GroupedOpenApi vendorQuotationApi() {
        return GroupedOpenApi.builder()
                .group("Vendor Quotation APIs")
                .pathsToMatch("/api/vendor-quotations/**")
                .build();
    }

    @Bean
    public GroupedOpenApi allApis() {
        return GroupedOpenApi.builder()
                .group("All APIs")
                .pathsToMatch("/api/**")
                .build();
    }
}