package com.synergy.service;

import com.synergy.dto.VendorPropertyDTO;
import com.synergy.entity.VendorPropertyEntity;
import com.synergy.repository.VendorPropertyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class VendorPropertyService {

    @Autowired
    private VendorPropertyRepository vendorPropertyRepository;

    public List<VendorPropertyDTO> getAllVendorProperties() {
        return vendorPropertyRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private VendorPropertyDTO convertToDTO(VendorPropertyEntity entity) {
        VendorPropertyDTO dto = new VendorPropertyDTO();
        dto.setId(entity.getId());
        dto.setItems(entity.getItems());
        dto.setVendorType(entity.getVendorType());
        return dto;
    }
}
