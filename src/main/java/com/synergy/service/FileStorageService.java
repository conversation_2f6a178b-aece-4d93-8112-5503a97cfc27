package com.synergy.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Service
public class FileStorageService {

    @Value("${file.upload-dir:uploads/items}")
    private String uploadDir;

    /**
     * Generates a full URL for an image path
     * @param imagePath The relative path of the image
     * @return The full URL to access the image
     */
    public String getImageUrl(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            return null;
        }
        
        // Build the URL using the current request context
        return ServletUriComponentsBuilder.fromCurrentContextPath()
                .path("/uploads/items/")
                .path(imagePath)
                .toUriString();
    }
    
    /**
     * Generates a unique filename for an image
     * @param originalFilename The original filename
     * @param extension The file extension
     * @return A unique filename
     */
    public String generateUniqueFilename(String originalFilename, String extension) {
        return UUID.randomUUID().toString() + "." + extension;
    }
    
    /**
     * Gets the upload directory path
     * @return The Path object for the upload directory
     */
    public Path getUploadPath() {
        return Paths.get(uploadDir);
    }
}
