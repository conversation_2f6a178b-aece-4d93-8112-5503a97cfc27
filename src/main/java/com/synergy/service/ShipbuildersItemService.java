package com.synergy.service;

import com.synergy.dto.ShipbuildersItemDropdownDTO;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.repository.ShipbuildersItemRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Service
public class ShipbuildersItemService {

    private static final Logger logger = LoggerFactory.getLogger(ShipbuildersItemService.class);

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Cacheable("itemsList")
    public List<ShipbuildersItemEntity> getAllItems() {
        return shipbuildersItemRepository.findAll();
    }

    @Cacheable("itemsDropdown")
    public List<ShipbuildersItemDropdownDTO> getAllItemsForDropdown() {
        return shipbuildersItemRepository.findAllForDropdown();
    }

    @Cacheable(value = "itemById", key = "#id")
    public Optional<ShipbuildersItemEntity> getItemById(Long id) {
        logger.info("Fetching item with ID: {} from repository or cache", id);
        Optional<ShipbuildersItemEntity> item = shipbuildersItemRepository.findById(id);
        if (item.isPresent()) {
            logger.info("Found item with ID: {}, name: {}", id, item.get().getItemName());
        } else {
            logger.warn("Item with ID: {} not found in repository", id);
        }
        return item;
    }

    @Cacheable(value = "itemsBySubCategory", key = "#subCategory")
    public List<ShipbuildersItemEntity> findBySubCategory(String subCategory) {
        return shipbuildersItemRepository.findBySubCategoryContainingIgnoreCase(subCategory);
    }

    @Cacheable(value = "itemsSearch", key = "#subCategory + '_' + #description + '_' + #itemCode")
    public List<ShipbuildersItemEntity> searchItems(String subCategory, String description, String itemCode) {
        return shipbuildersItemRepository.searchItems(subCategory, description, itemCode);
    }

    @CacheEvict(value = {"itemsList", "itemsDropdown", "itemById", "itemsBySubCategory", "itemsSearch"}, allEntries = true)
    public ShipbuildersItemEntity saveItem(ShipbuildersItemEntity item) {
        logger.info("Saving item with ID: {}, name: {}", item.getId(), item.getItemName());

        // Auto-generate itemCode if not provided
        if (item.getItemCode() == null || item.getItemCode().trim().isEmpty()) {
            long count = shipbuildersItemRepository.count();
            String generatedCode = item.getCategory().substring(0, 3).toUpperCase() + item.getSubCategory().substring(0, 3).toUpperCase() + String.format("%03d", count + 1);
            item.setItemCode(generatedCode);
            logger.info("Auto-generated item code: {}", generatedCode);
        }

        // Save the item
        ShipbuildersItemEntity savedItem = shipbuildersItemRepository.save(item);

        if (item.getId() != null) {
            logger.info("Evicting item with ID: {} from cache", item.getId());
            clearItemCache(item.getId());
        }

        logger.info("Item saved successfully. ID: {}, name: {}", savedItem.getId(), savedItem.getItemName());
        return savedItem;
    }

    @CacheEvict(value = {"itemsList"}, allEntries = true)
    public void deleteById(Long id) {
        shipbuildersItemRepository.deleteById(id);
    }

    /**
     * Explicitly clear the cache for a specific item ID
     */
    @CacheEvict(value = "itemById", key = "#id")
    public void clearItemCache(Long id) {
        logger.info("Explicitly clearing cache for item ID: {}", id);
    }

    @CacheEvict(value = {"itemsList", "itemsDropdown", "itemById", "itemsBySubCategory", "itemsSearch"}, allEntries = true)
    public Map<String, Object> bulkUploadItems(MultipartFile file) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<ShipbuildersItemEntity> successfulItems = new ArrayList<>();
        int totalRows = 0;
        int successCount = 0;
        int errorCount = 0;

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // Skip header row (row 0)
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                totalRows++;

                try {
                    ShipbuildersItemEntity item = parseRowToItem(row, i + 1);
                    if (item != null) {
                        ShipbuildersItemEntity savedItem = shipbuildersItemRepository.save(item);
                        successfulItems.add(savedItem);
                        successCount++;
                        logger.info("Successfully saved item: {} at row {}", item.getItemName(), i + 1);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String errorMsg = "Row " + (i + 1) + ": " + e.getMessage();
                    errors.add(errorMsg);
                    logger.error("Error processing row {}: {}", i + 1, e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("Error reading Excel file: {}", e.getMessage());
            throw new IOException("Error reading Excel file: " + e.getMessage());
        }

        result.put("totalRows", totalRows);
        result.put("successCount", successCount);
        result.put("errorCount", errorCount);
        result.put("errors", errors);
        result.put("successfulItems", successfulItems.size());

        logger.info("Bulk upload completed. Total: {}, Success: {}, Errors: {}", totalRows, successCount, errorCount);
        return result;
    }

    private ShipbuildersItemEntity parseRowToItem(Row row, int rowNumber) {
        ShipbuildersItemEntity item = new ShipbuildersItemEntity();

        try {
            // Column mapping based on typical Excel structure
            item.setItemName(getCellValueAsString(row.getCell(0))); // A: Item Name
            item.setCategory(getCellValueAsString(row.getCell(1))); // B: Category
            item.setSubCategory(getCellValueAsString(row.getCell(2))); // C: Sub Category
            item.setQualityCheck(getCellValueAsString(row.getCell(3))); // D: Quality Check
            item.setItemCode(getCellValueAsString(row.getCell(4))); // E: Item Code
            item.setSpecification1(getCellValueAsString(row.getCell(5))); // F: Specification 1
            item.setDropdown(getCellValueAsString(row.getCell(6))); // G: Dropdown
            item.setSpecification2(getCellValueAsString(row.getCell(7))); // H: Specification 2
            item.setSpecification3(getCellValueAsString(row.getCell(8))); // I: Specification 3
            item.setMaterialDescription(getCellValueAsString(row.getCell(9))); // J: Material Description
            item.setMaterial(getCellValueAsString(row.getCell(10))); // K: Material
            item.setPurchasingUOM(getCellValueAsString(row.getCell(11))); // L: Purchasing UOM
            item.setInventoryUOM(getCellValueAsString(row.getCell(12))); // M: Inventory UOM
            item.setQtyPerPack(getCellValueAsFloat(row.getCell(13))); // N: Qty Per Pack
            item.setReorderLevel(getCellValueAsFloat(row.getCell(14))); // O: Reorder Level
            item.setLastRate(getCellValueAsFloat(row.getCell(15))); // P: Last Rate
            item.setGstSlab(getCellValueAsFloat(row.getCell(16))); // Q: GST Slab
            item.setUnitOfMeasure(getCellValueAsString(row.getCell(17))); // R: Unit of Measure
            item.setDescription(getCellValueAsString(row.getCell(18))); // S: Description

            // Validate required fields
            if (item.getItemName() == null || item.getItemName().trim().isEmpty()) {
                throw new IllegalArgumentException("Item Name is required");
            }

            // Auto-generate itemCode if not provided
            if (item.getItemCode() == null || item.getItemCode().trim().isEmpty()) {
                if (item.getCategory() != null && item.getSubCategory() != null) {
                    long count = shipbuildersItemRepository.count();
                    String generatedCode = item.getCategory().substring(0, Math.min(3, item.getCategory().length())).toUpperCase() +
                                         item.getSubCategory().substring(0, Math.min(3, item.getSubCategory().length())).toUpperCase() +
                                         String.format("%03d", count + 1);
                    item.setItemCode(generatedCode);
                }
            }

            return item;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing row data: " + e.getMessage());
        }
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    private Float getCellValueAsFloat(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case NUMERIC:
                return (float) cell.getNumericCellValue();
            case STRING:
                try {
                    return Float.parseFloat(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    public void generateBulkUploadTemplate(jakarta.servlet.http.HttpServletResponse response) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Items Template");

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Item Name*", "Category", "Sub Category", "Quality Check", "Item Code",
                "Specification 1", "Dropdown", "Specification 2", "Specification 3",
                "Material Description", "Material", "Purchasing UOM", "Inventory UOM",
                "Qty Per Pack", "Reorder Level", "Last Rate", "GST Slab",
                "Unit of Measure", "Description"
            };

            // Create header style
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            // Apply headers
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            // Add sample data rows
            Row sampleRow1 = sheet.createRow(1);
            sampleRow1.createCell(0).setCellValue("Steel Rod 12mm");
            sampleRow1.createCell(1).setCellValue("Production");
            sampleRow1.createCell(2).setCellValue("Metal");
            sampleRow1.createCell(3).setCellValue("IQC Required");
            sampleRow1.createCell(4).setCellValue("STL001");
            sampleRow1.createCell(5).setCellValue("Length: 6m");
            sampleRow1.createCell(6).setCellValue("Standard");
            sampleRow1.createCell(7).setCellValue("Diameter: 12mm");
            sampleRow1.createCell(8).setCellValue("Grade: Fe500");
            sampleRow1.createCell(9).setCellValue("High strength steel rod for construction");
            sampleRow1.createCell(10).setCellValue("Steel");
            sampleRow1.createCell(11).setCellValue("Meter");
            sampleRow1.createCell(12).setCellValue("Meter");
            sampleRow1.createCell(13).setCellValue(1.0f);
            sampleRow1.createCell(14).setCellValue(50.0f);
            sampleRow1.createCell(15).setCellValue(450.0f);
            sampleRow1.createCell(16).setCellValue(18.0f);
            sampleRow1.createCell(17).setCellValue("PCS");
            sampleRow1.createCell(18).setCellValue("Steel reinforcement rod");

            Row sampleRow2 = sheet.createRow(2);
            sampleRow2.createCell(0).setCellValue("Bolt M10x50");
            sampleRow2.createCell(1).setCellValue("Assembly");
            sampleRow2.createCell(2).setCellValue("Fastener");
            sampleRow2.createCell(3).setCellValue("Visual Check");
            sampleRow2.createCell(4).setCellValue("BOL001");
            sampleRow2.createCell(5).setCellValue("Thread: M10");
            sampleRow2.createCell(6).setCellValue("Standard");
            sampleRow2.createCell(7).setCellValue("Length: 50mm");
            sampleRow2.createCell(8).setCellValue("Material: SS304");
            sampleRow2.createCell(9).setCellValue("Stainless steel hex bolt");
            sampleRow2.createCell(10).setCellValue("Stainless Steel");
            sampleRow2.createCell(11).setCellValue("Meter");
            sampleRow2.createCell(12).setCellValue("Meter");
            sampleRow2.createCell(13).setCellValue(100.0f);
            sampleRow2.createCell(14).setCellValue(500.0f);
            sampleRow2.createCell(15).setCellValue(15.0f);
            sampleRow2.createCell(16).setCellValue(18.0f);
            sampleRow2.createCell(17).setCellValue("PCS");
            sampleRow2.createCell(18).setCellValue("Hex head bolt for assembly");

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Set response headers
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=items_bulk_upload_template.xlsx");

            // Write to response
            workbook.write(response.getOutputStream());
            logger.info("Bulk upload template generated successfully");

        } catch (Exception e) {
            logger.error("Error generating bulk upload template: {}", e.getMessage());
            throw new IOException("Error generating template: " + e.getMessage());
        }
    }

}
