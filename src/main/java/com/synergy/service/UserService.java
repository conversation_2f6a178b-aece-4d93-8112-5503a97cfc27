package com.synergy.service;

import com.synergy.entity.UserEntity;
import com.synergy.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    public List<UserEntity> getAllUsers() {
        return userRepository.findAll();
    }

    public UserEntity getUserById(Long id) {
        return userRepository.findById(id).orElseThrow(() ->
            new RuntimeException("User not found with id: " + id));
    }

    public UserEntity createUser(UserEntity user) {
        // Validate required fields
        validateUserFields(user);

        // Check for unique constraints
        validateUniqueConstraints(user);

        // Auto-generate date and serial number
        user.setDate(LocalDate.now());
        user.setSerialNo(generateNextSerialNumber());

        // Save and return the complete user entity
        return userRepository.save(user);
    }

    private void validateUserFields(UserEntity user) {
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            throw new RuntimeException("Username is required");
        }
        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new RuntimeException("Email is required");
        }
        if (user.getEmployeeName() == null || user.getEmployeeName().trim().isEmpty()) {
            throw new RuntimeException("Employee name is required");
        }
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            throw new RuntimeException("Password is required");
        }
        if (user.getRole() == null || user.getRole().trim().isEmpty()) {
            throw new RuntimeException("Role is required");
        }
    }

    private void validateUniqueConstraints(UserEntity user) {
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("Username already exists");
        }
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("Email already exists");
        }
    }

    private String generateNextSerialNumber() {
        List<UserEntity> allUsers = userRepository.findAll();
        int maxSerialNo = 0;

        for (UserEntity existingUser : allUsers) {
            if (existingUser.getSerialNo() != null) {
                String serialStr = existingUser.getSerialNo().replaceFirst("^0+(?!$)", "");
                try {
                    int serialNo = Integer.parseInt(serialStr);
                    maxSerialNo = Math.max(maxSerialNo, serialNo);
                } catch (NumberFormatException e) {
                    // Skip if not a number
                }
            }
        }

        return String.format("%03d", maxSerialNo + 1);
    }

    public UserEntity updateUser(Long id, UserEntity user) {
        UserEntity existingUser = getUserById(id);

        if (user.getUsername() != null && !user.getUsername().isEmpty()
            && !user.getUsername().equals(existingUser.getUsername())) {
            if (userRepository.existsByUsername(user.getUsername())) {
                throw new RuntimeException("Username already exists");
            }
            existingUser.setUsername(user.getUsername());
        }

        if (user.getEmail() != null && !user.getEmail().isEmpty()
            && !user.getEmail().equals(existingUser.getEmail())) {
            if (userRepository.existsByEmail(user.getEmail())) {
                throw new RuntimeException("Email already exists");
            }
            existingUser.setEmail(user.getEmail());
        }

        if (user.getEmployeeName() != null && !user.getEmployeeName().isEmpty()) {
            existingUser.setEmployeeName(user.getEmployeeName());
        }

        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            existingUser.setPassword(user.getPassword());
        }

        if (user.getRole() != null && !user.getRole().isEmpty()) {
            existingUser.setRole(user.getRole());
        }

        return userRepository.save(existingUser);
    }

    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    public UserEntity findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public boolean validatePassword(String rawPassword, String storedPassword) {
        return rawPassword.equals(storedPassword);
    }

    public UserEntity save(UserEntity user) {
        // Add validation logic here if needed
        return userRepository.save(user);
    }
}