package com.synergy.service;

import com.synergy.dto.GRNFormDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.entity.GRNEntity;
import com.synergy.entity.GRNLineItemEntity;
import com.synergy.entity.GatePassEntity;
import com.synergy.entity.GatePassLineItemEntity;
import com.synergy.repository.ShipbuildersItemRepository;
import com.synergy.repository.GRNRepository;
import com.synergy.repository.GatePassRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.Date;

@Service
public class GRNService {

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Autowired
    private GRNRepository grnRepository;

    @Autowired
    private BillService billService;

    @Autowired
    private GatePassRepository gatePassRepository;

    @Autowired
    private GatePassService gatePassService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Get GRN form data for a gate pass
     */
    public GRNFormDTO getGRNFormByGatePassId(String gatePassId) {
        // Check if GRN already exists for this gate pass
        Optional<GRNEntity> existingGrnOpt = grnRepository.findByGatePassId(gatePassId);

        if (existingGrnOpt.isPresent()) {
            // Return saved GRN data
            return convertGRNEntityToFormDTO(existingGrnOpt.get());
        }

        // If no GRN exists, create form from gate pass data
        // Extract vendor PO ID from gate pass ID (format: PO{vendorPoId}-GP{sequence})
        String vendorPoId = extractVendorPoIdFromGatePassId(gatePassId);

        // Check if this is GP01 or GP02+ by checking if gate pass exists
        Optional<GatePassEntity> gatePassOpt = gatePassRepository.findByGatePassId(gatePassId);

        // Create GRN form DTO
        GRNFormDTO grnForm = new GRNFormDTO();
        grnForm.setGatePassId(gatePassId);
        grnForm.setVendorPoId(vendorPoId);

        // Process line items and check for quality requirements
        List<GRNFormDTO.GRNLineItemDTO> grnLineItems = new ArrayList<>();
        boolean hasQualityCheck = false;

        if (gatePassOpt.isPresent() && !gatePassOpt.get().getLineItems().isEmpty()) {
            // GP02+ - Use stored remaining items from gate pass
            GatePassEntity gatePass = gatePassOpt.get();
            grnForm.setYardNumber(gatePass.getYardNumber());
            grnForm.setProjectName(gatePass.getProjectName());
            grnForm.setVendorCompanyName(gatePass.getVendorCompanyName());

            for (GatePassLineItemEntity item : gatePass.getLineItems()) {
                GRNFormDTO.GRNLineItemDTO grnItem = new GRNFormDTO.GRNLineItemDTO();
                grnItem.setItemCode(item.getItemCode());
                grnItem.setDescription(item.getDescription());
                grnItem.setMaterialFamily(item.getMaterialFamily());
                grnItem.setQtyOrdered(item.getQtyOrdered());
                grnItem.setQtyReceived(0); // Default to 0 for new GRN
                grnItem.setStatus("Pending"); // Default status
                grnItem.setLocation("Yard"); // Default location
                grnItem.setRemarks("Enter Remarks"); // Default remarks
                grnItem.setUniqueCode(item.getUniqueCode());

                // Get quality check requirement from shipbuilders item
                String qualityCheckRequirement = getQualityCheckRequirement(item.getUniqueCode());
                grnItem.setQualityCheckRequirement(qualityCheckRequirement);

                // Check if this item requires quality check
                if (qualityCheckRequirement != null &&
                    !qualityCheckRequirement.equalsIgnoreCase("not required")) {
                    hasQualityCheck = true;
                }

                grnLineItems.add(grnItem);
            }
        } else {
            // GP01 - Use original vendor PO data
            VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);
            grnForm.setYardNumber(vendorPO.getYardNumber());
            grnForm.setProjectName(vendorPO.getProjectName());
            grnForm.setVendorCompanyName(vendorPO.getVendorCompanyName());

            if (vendorPO.getLineItems() != null) {
                for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
                    GRNFormDTO.GRNLineItemDTO grnItem = new GRNFormDTO.GRNLineItemDTO();
                grnItem.setItemCode(item.getItemCode());
                grnItem.setDescription(item.getDescription());
                grnItem.setMaterialFamily(item.getMaterialFamily());
                grnItem.setQtyOrdered(item.getQuantity());
                grnItem.setQtyReceived(0); // Default to 0, will be filled by user
                grnItem.setStatus("Pending"); // Default status
                grnItem.setLocation("Yard"); // Default location
                grnItem.setRemarks("Enter Remarks"); // Default remarks
                grnItem.setUniqueCode(item.getUniqueCode());
                
                // Get quality check requirement from shipbuilders item
                String qualityCheckRequirement = getQualityCheckRequirement(item.getUniqueCode());
                grnItem.setQualityCheckRequirement(qualityCheckRequirement);
                
                // Check if this item requires quality check
                if (qualityCheckRequirement != null && 
                    !qualityCheckRequirement.equalsIgnoreCase("not required")) {
                    hasQualityCheck = true;
                }
                
                grnLineItems.add(grnItem);
                }
            }
        }

        grnForm.setLineItems(grnLineItems);
        grnForm.setQualityCheck(hasQualityCheck);

        return grnForm;
    }
    
    /**
     * Extract vendor PO ID from gate pass ID
     */
    private String extractVendorPoIdFromGatePassId(String gatePassId) {
        // Gate pass ID format: PO{vendorPoId}-GP{sequence}
        // Example: PO647-1-GP01 -> 647-1
        if (gatePassId.startsWith("PO") && gatePassId.contains("-GP")) {
            String withoutPO = gatePassId.substring(2); // Remove "PO"
            int gpIndex = withoutPO.lastIndexOf("-GP");
            if (gpIndex > 0) {
                return withoutPO.substring(0, gpIndex);
            }
        }
        throw new IllegalArgumentException("Invalid gate pass ID format: " + gatePassId);
    }
    
    /**
     * Get quality check requirement for an item by unique code
     * Note: uniqueCode corresponds to itemCode in ShipbuildersItemEntity
     */
    private String getQualityCheckRequirement(String uniqueCode) {
        try {
            if (uniqueCode == null || uniqueCode.trim().isEmpty()) {
                return "not required";
            }

            // uniqueCode corresponds to itemCode in ShipbuildersItemEntity
            Optional<ShipbuildersItemEntity> itemOpt = shipbuildersItemRepository.findByItemCode(uniqueCode);
            if (itemOpt.isPresent()) {
                String qualityCheck = itemOpt.get().getQualityCheck();
                return qualityCheck != null ? qualityCheck : "not required";
            }
        } catch (Exception e) {
            System.out.println("Error getting quality check for unique code " + uniqueCode + ": " + e.getMessage());
        }
        return "not required"; // Default fallback
    }

    /**
     * Submit GRN form data with enhanced workflow
     */
    public GRNEntity submitGRN(GRNFormDTO grnFormDTO) {
        // Check if GRN already exists for this gate pass
        Optional<GRNEntity> existingGrnOpt = grnRepository.findByGatePassId(grnFormDTO.getGatePassId());

        GRNEntity grn;
        if (existingGrnOpt.isPresent()) {
            // Update existing DRAFT GRN to SUBMITTED
            grn = existingGrnOpt.get();

            // Only allow submission if status is DRAFT
            if (!"DRAFT".equals(grn.getStatus())) {
                throw new IllegalArgumentException("GRN with status '" + grn.getStatus() + "' cannot be submitted. Only DRAFT GRNs can be submitted.");
            }
        } else {
            // Create new GRN if it doesn't exist (fallback for direct submission)
            grn = new GRNEntity();
            grn.setGrnNumber(generateGrnNumber());
            grn.setGatePassId(grnFormDTO.getGatePassId());
            grn.setCreatedDate(new Date());
        }

        // Validate line items and apply status logic
        validateAndProcessLineItems(grnFormDTO);

        // Update GRN fields with submitted data
        grn.setVendorPoId(grnFormDTO.getVendorPoId());
        grn.setYardNumber(grnFormDTO.getYardNumber());
        grn.setProjectName(grnFormDTO.getProjectName());
        grn.setVendorCompanyName(grnFormDTO.getVendorCompanyName());
        grn.setQualityCheck(grnFormDTO.getQualityCheck());
        grn.setLineItemCount(grnFormDTO.getLineItems() != null ? grnFormDTO.getLineItems().size() : 0);
        grn.setStatus("SUBMITTED");
        grn.setSubmittedDate(new Date());

        // Update line items
        grn.getLineItems().clear();
        if (grnFormDTO.getLineItems() != null) {
            for (GRNFormDTO.GRNLineItemDTO itemDTO : grnFormDTO.getLineItems()) {
                GRNLineItemEntity lineItem = new GRNLineItemEntity();
                lineItem.setGrn(grn);
                lineItem.setItemCode(itemDTO.getItemCode());
                lineItem.setDescription(itemDTO.getDescription());
                lineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                lineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                lineItem.setQtyReceived(itemDTO.getQtyReceived());
                lineItem.setStatus(itemDTO.getStatus());
                lineItem.setLocation(itemDTO.getLocation());
                lineItem.setRemarks(itemDTO.getRemarks());
                lineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                lineItem.setUniqueCode(itemDTO.getUniqueCode());
                if (itemDTO.getAttachmentImages() != null && !itemDTO.getAttachmentImages().isEmpty()) {
                    String imagePaths = processAttachmentImages(itemDTO.getAttachmentImages());
                    lineItem.setAttachmentImagePaths(imagePaths);
                }
                grn.getLineItems().add(lineItem);
            }
        }

        // Save GRN
        GRNEntity savedGRN = grnRepository.save(grn);

        // Process GRN submission workflow (create GP02 and bills) - ONLY on POST submission
        processGRNWorkflow(savedGRN, grnFormDTO);

        return savedGRN;
    }

    /**
     * Update GRN by Gate Pass ID (Create if not exists)
     */
    public GRNEntity updateGRNByGatePassId(String gatePassId, GRNFormDTO grnFormDTO) {
        Optional<GRNEntity> existingGrnOpt = grnRepository.findByGatePassId(gatePassId);

        GRNEntity grn;
        if (existingGrnOpt.isEmpty()) {
            // Create new GRN in DRAFT status if it doesn't exist
            grn = new GRNEntity();
            grn.setGrnNumber(generateGrnNumber());
            grn.setGatePassId(gatePassId);
            grn.setStatus("DRAFT");
            grn.setCreatedDate(new Date());
        } else {
            // Update existing GRN
            grn = existingGrnOpt.get();

            // Only allow updates if status is DRAFT or SUBMITTED
            if (!"DRAFT".equals(grn.getStatus()) && !"SUBMITTED".equals(grn.getStatus())) {
                throw new IllegalArgumentException("Cannot update GRN with status: " + grn.getStatus());
            }
        }

        // Update GRN fields
        grn.setVendorPoId(grnFormDTO.getVendorPoId());
        grn.setYardNumber(grnFormDTO.getYardNumber());
        grn.setProjectName(grnFormDTO.getProjectName());
        grn.setVendorCompanyName(grnFormDTO.getVendorCompanyName());
        grn.setQualityCheck(grnFormDTO.getQualityCheck());
        grn.setLineItemCount(grnFormDTO.getLineItems() != null ? grnFormDTO.getLineItems().size() : 0);

        // Update only the line items provided in the request (preserve existing ones not included)
        if (grnFormDTO.getLineItems() != null) {
            for (GRNFormDTO.GRNLineItemDTO itemDTO : grnFormDTO.getLineItems()) {
                // Find existing line item by uniqueCode
                GRNLineItemEntity existingLineItem = grn.getLineItems().stream()
                    .filter(item -> item.getUniqueCode().equals(itemDTO.getUniqueCode()))
                    .findFirst()
                    .orElse(null);

                if (existingLineItem != null) {
                    // Update existing line item
                    existingLineItem.setDescription(itemDTO.getDescription());
                    existingLineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                    existingLineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                    existingLineItem.setQtyReceived(itemDTO.getQtyReceived());
                    existingLineItem.setStatus(itemDTO.getStatus());
                    existingLineItem.setLocation(itemDTO.getLocation());
                    existingLineItem.setRemarks(itemDTO.getRemarks());
                    existingLineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                    existingLineItem.setUniqueCode(itemDTO.getUniqueCode());
                    if (itemDTO.getAttachmentImages() != null && !itemDTO.getAttachmentImages().isEmpty()) {
                        String imagePaths = processAttachmentImages(itemDTO.getAttachmentImages());
                        existingLineItem.setAttachmentImagePaths(imagePaths);
                    }
                } else {
                    // Add new line item if it doesn't exist
                    GRNLineItemEntity lineItem = new GRNLineItemEntity();
                    lineItem.setGrn(grn);
                    lineItem.setItemCode(itemDTO.getItemCode());
                    lineItem.setDescription(itemDTO.getDescription());
                    lineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                    lineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                    lineItem.setQtyReceived(itemDTO.getQtyReceived());
                    lineItem.setStatus(itemDTO.getStatus());
                    lineItem.setLocation(itemDTO.getLocation());
                    lineItem.setRemarks(itemDTO.getRemarks());
                    lineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                    lineItem.setUniqueCode(itemDTO.getUniqueCode());
                    if (itemDTO.getAttachmentImages() != null && !itemDTO.getAttachmentImages().isEmpty()) {
                        String imagePaths = processAttachmentImages(itemDTO.getAttachmentImages());
                        lineItem.setAttachmentImagePaths(imagePaths);
                    }
                    grn.getLineItems().add(lineItem);
                }
            }
        }

        return grnRepository.save(grn);
    }

    /**
     * Update GRN
     */
    public GRNEntity updateGRN(Long grnId, GRNFormDTO grnFormDTO) {
        Optional<GRNEntity> existingGrnOpt = grnRepository.findById(grnId);
        if (existingGrnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with ID: " + grnId);
        }

        GRNEntity grn = existingGrnOpt.get();

        // Only allow updates if status is DRAFT or SUBMITTED
        if (!"DRAFT".equals(grn.getStatus()) && !"SUBMITTED".equals(grn.getStatus())) {
            throw new IllegalArgumentException("Cannot update GRN with status: " + grn.getStatus());
        }

        // Update GRN fields
        grn.setYardNumber(grnFormDTO.getYardNumber());
        grn.setProjectName(grnFormDTO.getProjectName());
        grn.setVendorCompanyName(grnFormDTO.getVendorCompanyName());
        grn.setQualityCheck(grnFormDTO.getQualityCheck());
        grn.setLineItemCount(grnFormDTO.getLineItems() != null ? grnFormDTO.getLineItems().size() : 0);

        // Clear existing line items and add new ones
        grn.getLineItems().clear();
        if (grnFormDTO.getLineItems() != null) {
            for (GRNFormDTO.GRNLineItemDTO itemDTO : grnFormDTO.getLineItems()) {
                GRNLineItemEntity lineItem = new GRNLineItemEntity();
                lineItem.setGrn(grn);
                lineItem.setItemCode(itemDTO.getItemCode());
                lineItem.setDescription(itemDTO.getDescription());
                lineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                lineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                lineItem.setQtyReceived(itemDTO.getQtyReceived());
                lineItem.setStatus(itemDTO.getStatus());
                lineItem.setLocation(itemDTO.getLocation());
                lineItem.setRemarks(itemDTO.getRemarks());
                lineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                lineItem.setUniqueCode(itemDTO.getUniqueCode());
                if (itemDTO.getAttachmentImages() != null && !itemDTO.getAttachmentImages().isEmpty()) {
                    String imagePaths = processAttachmentImages(itemDTO.getAttachmentImages());
                    lineItem.setAttachmentImagePaths(imagePaths);
                }
                grn.getLineItems().add(lineItem);
            }
        }

        return grnRepository.save(grn);
    }

    /**
     * Delete GRN
     */
    public void deleteGRN(Long grnId) {
        Optional<GRNEntity> existingGrnOpt = grnRepository.findById(grnId);
        if (existingGrnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with ID: " + grnId);
        }

        GRNEntity grn = existingGrnOpt.get();

        // Only allow deletion if status is DRAFT
        if (!"DRAFT".equals(grn.getStatus())) {
            throw new IllegalArgumentException("Cannot delete GRN with status: " + grn.getStatus());
        }

        grnRepository.deleteById(grnId);
    }

    /**
     * Generate GRN number
     */
    private String generateGrnNumber() {
        Long sequence = grnRepository.getNextGrnSequence();
        return String.format("GRN%04d", sequence);
    }

    /**
     * Validate line items and apply status logic
     */
    private void validateAndProcessLineItems(GRNFormDTO grnFormDTO) {
        if (grnFormDTO.getLineItems() == null || grnFormDTO.getLineItems().isEmpty()) {
            throw new IllegalArgumentException("GRN must have at least one line item");
        }

        for (GRNFormDTO.GRNLineItemDTO item : grnFormDTO.getLineItems()) {
            Integer qtyReceived = item.getQtyReceived();
            Integer qtyOrdered = item.getQtyOrdered();
            String status = item.getStatus();

            // Validation: Received qty must not be zero with status Passed
            if (qtyReceived != null && qtyReceived == 0 && "Passed".equalsIgnoreCase(status)) {
                throw new IllegalArgumentException("Received qty must not be zero with status Passed for item: " + item.getItemCode());
            }

            // Apply automatic status logic based on received quantity
            if (qtyReceived == null || qtyReceived == 0) {
                item.setStatus("Pending");
            } else if (qtyReceived.equals(qtyOrdered)) {
                item.setStatus("Passed");
            } else if (qtyReceived > 0 && qtyReceived < qtyOrdered) {
                item.setStatus("Partial");
            }
        }
    }

    /**
     * Process GRN workflow: create new gate pass for pending/partial items and prepare for bill creation
     */
    private void processGRNWorkflow(GRNEntity grn, GRNFormDTO grnFormDTO) {
        List<GRNFormDTO.GRNLineItemDTO> pendingItems = new ArrayList<>();
        List<GRNFormDTO.GRNLineItemDTO> partialItems = new ArrayList<>();
        List<GRNFormDTO.GRNLineItemDTO> billItems = new ArrayList<>();

        // Categorize line items based on status
        for (GRNFormDTO.GRNLineItemDTO item : grnFormDTO.getLineItems()) {
            String status = item.getStatus();

            if ("Pending".equalsIgnoreCase(status)) {
                pendingItems.add(item);
            } else if ("Partial".equalsIgnoreCase(status)) {
                partialItems.add(item);
                billItems.add(item); // Partial items go to both new gate pass and bill
            } else if ("Passed".equalsIgnoreCase(status) || "Missing".equalsIgnoreCase(status) || "Damage".equalsIgnoreCase(status)) {
                billItems.add(item); // Passed, Missing, and Damage items go to bill
            }
        }

        // Create new gate pass for pending items and remaining quantities of partial items
        if (!pendingItems.isEmpty() || !partialItems.isEmpty()) {
            createNewGatePassForRemainingItems(grn.getVendorPoId(), pendingItems, partialItems);
        }

        // Create bill for items that are not pending
        if (!billItems.isEmpty()) {
            billService.createBillFromGRN(grn, billItems);
        }
    }

    /**
     * Create new gate pass for pending and partial items
     */
    private void createNewGatePassForRemainingItems(String vendorPoId,
                                                   List<GRNFormDTO.GRNLineItemDTO> pendingItems,
                                                   List<GRNFormDTO.GRNLineItemDTO> partialItems) {
        List<GatePassService.RemainingItemDTO> remainingItems = new ArrayList<>();

        // Add pending items with their full ordered quantity
        for (GRNFormDTO.GRNLineItemDTO item : pendingItems) {
            GatePassService.RemainingItemDTO remainingItem = new GatePassService.RemainingItemDTO(
                item.getItemCode(),
                item.getDescription(),
                item.getMaterialFamily(),
                item.getQtyOrdered(), // Full ordered quantity for pending items
                item.getUniqueCode(),
                "Pending"
            );
            remainingItems.add(remainingItem);
        }

        // Add partial items with remaining quantity (ordered - received)
        for (GRNFormDTO.GRNLineItemDTO item : partialItems) {
            Integer remainingQty = item.getQtyOrdered() - (item.getQtyReceived() != null ? item.getQtyReceived() : 0);
            if (remainingQty > 0) {
                GatePassService.RemainingItemDTO remainingItem = new GatePassService.RemainingItemDTO(
                    item.getItemCode(),
                    item.getDescription(),
                    item.getMaterialFamily(),
                    remainingQty,
                    item.getUniqueCode(),
                    "Partial"
                );
                remainingItems.add(remainingItem);
            }
        }

        // Create the new gate pass if there are remaining items
        if (!remainingItems.isEmpty()) {
            gatePassService.createGatePassForRemainingItems(vendorPoId, remainingItems);
        }
    }

    /**
     * Get all GRNs
     */
    public List<GRNEntity> getAllGRNs() {
        return grnRepository.findAllByOrderByCreatedDateDesc();
    }

    /**
     * Get GRNs by status
     */
    public List<GRNEntity> getGRNsByStatus(String status) {
        return grnRepository.findByStatusIgnoreCaseOrderByCreatedDateDesc(status);
    }

    /**
     * Get GRN by ID
     */
    public GRNEntity getGRNById(Long id) {
        Optional<GRNEntity> grnOpt = grnRepository.findById(id);
        if (grnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with ID: " + id);
        }
        return grnOpt.get();
    }

    /**
     * Get GRN by GRN number
     */
    public GRNEntity getGRNByNumber(String grnNumber) {
        Optional<GRNEntity> grnOpt = grnRepository.findByGrnNumber(grnNumber);
        if (grnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with number: " + grnNumber);
        }
        return grnOpt.get();
    }

    /**
     * Convert GRN entity to form DTO
     */
    private GRNFormDTO convertGRNEntityToFormDTO(GRNEntity grn) {
        GRNFormDTO grnForm = new GRNFormDTO();
        grnForm.setGatePassId(grn.getGatePassId());
        grnForm.setVendorPoId(grn.getVendorPoId());
        grnForm.setYardNumber(grn.getYardNumber());
        grnForm.setProjectName(grn.getProjectName());
        grnForm.setVendorCompanyName(grn.getVendorCompanyName());
        grnForm.setQualityCheck(grn.getQualityCheck());

        // Convert line items
        List<GRNFormDTO.GRNLineItemDTO> lineItemDTOs = new ArrayList<>();
        if (grn.getLineItems() != null) {
            for (GRNLineItemEntity lineItem : grn.getLineItems()) {
                GRNFormDTO.GRNLineItemDTO itemDTO = new GRNFormDTO.GRNLineItemDTO();
                itemDTO.setItemCode(lineItem.getItemCode());
                itemDTO.setDescription(lineItem.getDescription());
                itemDTO.setMaterialFamily(lineItem.getMaterialFamily());
                itemDTO.setQtyOrdered(lineItem.getQtyOrdered());
                itemDTO.setQtyReceived(lineItem.getQtyReceived());
                itemDTO.setStatus(lineItem.getStatus());
                itemDTO.setLocation(lineItem.getLocation());
                itemDTO.setRemarks(lineItem.getRemarks());
                itemDTO.setQualityCheckRequirement(lineItem.getQualityCheckRequirement());
                itemDTO.setUniqueCode(lineItem.getUniqueCode());
                if (lineItem.getAttachmentImagePaths() != null && !lineItem.getAttachmentImagePaths().isEmpty()) {
                    try {
                        List<String> imagePaths = objectMapper.readValue(lineItem.getAttachmentImagePaths(), List.class);
                        List<String> imageUrls = new ArrayList<>();
                        for (String imagePath : imagePaths) {
                            imageUrls.add(fileStorageService.getImageUrl(imagePath));
                        }
                        itemDTO.setAttachmentImages(imageUrls);
                    } catch (Exception e) {
                        System.out.println("Error parsing attachment image paths: " + e.getMessage());
                    }
                }
                lineItemDTOs.add(itemDTO);
            }
        }
        grnForm.setLineItems(lineItemDTOs);

        return grnForm;
    }

    private String processAttachmentImages(List<String> base64Images) {
        if (base64Images == null || base64Images.isEmpty()) {
            return null;
        }

        try {
            List<String> imagePaths = new ArrayList<>();

            for (String base64Image : base64Images) {
                if (base64Image != null && !base64Image.isEmpty()) {
                    String[] parts = base64Image.split(",");
                    String imageType = "png";
                    if (parts.length > 1 && parts[0].contains(":") && parts[0].contains(";")) {
                        imageType = parts[0].split(":")[1].split(";")[0].split("/")[1];
                    }
                    byte[] imageBytes = parts.length > 1 ? Base64.getDecoder().decode(parts[1])
                            : Base64.getDecoder().decode(base64Image);

                    String fileName = fileStorageService.generateUniqueFilename("grn-attachment", imageType);
                    Path uploadPath = fileStorageService.getUploadPath();

                    if (!Files.exists(uploadPath)) {
                        Files.createDirectories(uploadPath);
                    }

                    Path filePath = uploadPath.resolve(fileName);
                    Files.write(filePath, imageBytes);

                    imagePaths.add(fileName);
                }
            }

            return imagePaths.isEmpty() ? null : objectMapper.writeValueAsString(imagePaths);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process attachment images: " + e.getMessage());
        }
    }
}
