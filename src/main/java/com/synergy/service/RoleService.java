package com.synergy.service;

import com.synergy.entity.RoleEntity;
import com.synergy.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoleService {

    @Autowired
    private RoleRepository roleRepository;

    public List<RoleEntity> getAllRoles() {
        return roleRepository.findAll();
    }

    public RoleEntity getRoleById(Long id) {
        return roleRepository.findById(id).orElseThrow(() ->
            new RuntimeException("Role not found with id: " + id));
    }

    public RoleEntity getRoleByName(String name) {
        return roleRepository.findByName(name);
    }

    public RoleEntity createRole(RoleEntity role) {
        // Validate required fields
        if (role.getName() == null || role.getName().isEmpty()) {
            throw new RuntimeException("Role name is required");
        }

        // Check for unique constraints
        if (roleRepository.existsByName(role.getName())) {
            throw new RuntimeException("Role name already exists");
        }

        return roleRepository.save(role);
    }

    public RoleEntity updateRole(Long id, RoleEntity role) {
        RoleEntity existingRole = getRoleById(id);

        if (role.getName() != null && !role.getName().isEmpty()
            && !role.getName().equals(existingRole.getName())) {
            if (roleRepository.existsByName(role.getName())) {
                throw new RuntimeException("Role name already exists");
            }
            existingRole.setName(role.getName());
        }

        return roleRepository.save(existingRole);
    }

    public void deleteRole(Long id) {
        roleRepository.deleteById(id);
    }
}
