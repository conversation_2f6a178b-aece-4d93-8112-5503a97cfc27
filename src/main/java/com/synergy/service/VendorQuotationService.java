package com.synergy.service;

import com.synergy.dto.VendorQuotationDTO;
import com.synergy.dto.VendorQuotationItemDTO;
import com.synergy.dto.VendorQuotationChargeDTO;
import com.synergy.dto.VendorQuotationSelectionDTO;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.PurchaseRequestItemEntity;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.entity.VendorBiddingTokenEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.entity.VendorQuotationEntity;
import com.synergy.entity.VendorQuotationItemEntity;
import com.synergy.entity.VendorQuotationChargeEntity;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.ShipbuildersItemRepository;
import com.synergy.repository.VendorQuotationRepository;
import com.synergy.repository.VendorQuotationItemRepository;
import com.synergy.repository.VendorRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VendorQuotationService {

    private static final Logger logger = LoggerFactory.getLogger(VendorQuotationService.class);

    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;

    @Autowired
    private VendorQuotationItemRepository vendorQuotationItemRepository;

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @Autowired
    private VendorRepository vendorRepository;

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Autowired
    private VendorBiddingTokenService tokenService;

    @Transactional
    public VendorQuotationDTO submitQuotation(VendorQuotationDTO dto) {
        return submitQuotation(dto, null);
    }

    @Transactional
    public VendorQuotationDTO submitQuotation(VendorQuotationDTO dto, String token) {
        logger.info("Starting quotation submission for PR {} and vendor {} with token {}",
                dto.getPurchaseRequestId(), dto.getVendorId(), token != null ? "provided" : "not provided");

        // Validate token if provided
        VendorBiddingTokenEntity tokenEntity = null;
        if (token != null && !token.trim().isEmpty()) {
            Optional<VendorBiddingTokenEntity> tokenOpt = tokenService.validateToken(token);
            if (tokenOpt.isEmpty()) {
                throw new SecurityException(
                        "Invalid or expired bidding token. This link may have already been used or is no longer valid.");
            }
            tokenEntity = tokenOpt.get();

            // Verify that the token matches the PR and vendor in the DTO
            if (!tokenEntity.getPurchaseRequest().getId().equals(dto.getPurchaseRequestId()) ||
                    !tokenEntity.getVendor().getSrNo().equals(dto.getVendorId())) {
                throw new SecurityException("Token does not match the purchase request or vendor information.");
            }
            logger.info("Token validation successful for PR {} and vendor {}",
                    dto.getPurchaseRequestId(), dto.getVendorId());
        }

        // Validate purchase request and vendor
        logger.info("Looking up Purchase Request with ID: {}", dto.getPurchaseRequestId());
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(dto.getPurchaseRequestId())
                .orElseThrow(() -> new IllegalArgumentException(
                        "Purchase Request not found with ID: " + dto.getPurchaseRequestId()));

        logger.info("Looking up Vendor with srNo: {}", dto.getVendorId());
        VendorEntity vendor = vendorRepository.findById(dto.getVendorId())
                .orElseThrow(() -> new IllegalArgumentException("Vendor not found with srNo: " + dto.getVendorId()));

        // Check if vendor has already submitted a quotation for this PR
        logger.info("Checking for existing quotations for PR {} and vendor {}",
                dto.getPurchaseRequestId(), dto.getVendorId());
        List<VendorQuotationEntity> existingQuotations = vendorQuotationRepository
                .findByPurchaseRequestIdAndVendorSrNo(dto.getPurchaseRequestId(), dto.getVendorId());
        logger.info("Found {} existing quotations", existingQuotations.size());

        VendorQuotationEntity quotation;
        boolean isUpdate = false;

        if (!existingQuotations.isEmpty()) {
            // If token is provided (reshared link), allow updating existing quotation
            if (tokenEntity != null) {
                quotation = existingQuotations.get(0); // Update the existing quotation
                isUpdate = true;
                logger.info("Updating existing quotation {} for PR {} and vendor {} via reshared token",
                        quotation.getId(), dto.getPurchaseRequestId(), dto.getVendorId());

                // Update existing quotation - replace items collection entirely
                logger.info("Updating existing quotation {} with {} existing items",
                        quotation.getId(), quotation.getItems().size());

                quotation.setSubmissionDate(new Date()); // Update submission date
                quotation.setStatus("SUBMITTED");
                quotation.setCurrency(dto.getCurrency());
                quotation.setStockPoint(dto.getStockPoint());
                quotation.setReferenceNumber(dto.getReferenceNumber());

                // We'll replace the items collection after creating new items
            } else {
                // Direct API call without token - block multiple submissions
                throw new IllegalArgumentException(
                        "You have already submitted a quotation for this purchase request. Multiple submissions are not allowed.");
            }
        } else {
            // Create new quotation (first submission)
            quotation = new VendorQuotationEntity();
            quotation.setPurchaseRequest(pr);
            quotation.setVendor(vendor);
            quotation.setSubmissionDate(new Date());
            quotation.setStatus("SUBMITTED");
            quotation.setCurrency(dto.getCurrency());
            quotation.setStockPoint(dto.getStockPoint());
            quotation.setReferenceNumber(dto.getReferenceNumber());
            logger.info("Creating new quotation for PR {} and vendor {}",
                    dto.getPurchaseRequestId(), dto.getVendorId());
        }

        // Create/Update quotation items
        logger.info("Processing {} items for quotation", dto.getItems().size());
        List<VendorQuotationItemEntity> items = dto.getItems().stream()
                .map(itemDTO -> {
                    VendorQuotationItemEntity item = new VendorQuotationItemEntity();
                    item.setUniqueCode(itemDTO.getUniqueCode());
                    item.setProductName(itemDTO.getProductName());
                    item.setAvailableQuantity(itemDTO.getAvailableQuantity());
                    item.setUnitPrice(itemDTO.getUnitPrice());
                    item.setDeliveryDate(itemDTO.getDeliveryDate());

                    // Validate required fields
                    if (itemDTO.getAvailableQuantity() == null || itemDTO.getUnitPrice() == null) {
                        throw new IllegalArgumentException(
                                "Available quantity and unit price are required for item: " + itemDTO.getUniqueCode());
                    }

                    item.setTotalCost(itemDTO.getAvailableQuantity() * itemDTO.getUnitPrice());
                    item.setVendorRemarks(itemDTO.getVendorRemarks());
                    item.setVendorQuotation(quotation);
                    logger.debug("Created item: {} with quantity {} and price {}",
                            itemDTO.getUniqueCode(), itemDTO.getAvailableQuantity(), itemDTO.getUnitPrice());
                    return item;
                })
                .collect(Collectors.toList());

        // Create additional charges
        List<VendorQuotationChargeEntity> charges = new ArrayList<>();
        if (dto.getAdditionalCharges() != null) {
            charges = dto.getAdditionalCharges().stream()
                    .map(chargeDTO -> {
                        VendorQuotationChargeEntity charge = new VendorQuotationChargeEntity();
                        charge.setDescription(chargeDTO.getDescription());
                        charge.setAmount(chargeDTO.getAmount());
                        charge.setVendorQuotation(quotation);
                        return charge;
                    })
                    .collect(Collectors.toList());
        }

        // Handle items collection properly for updates vs new quotations
        if (isUpdate) {
            // For updates with partial quantity changes, handle selective updating
            logger.info("Updating existing quotation with {} existing items using {} new items",
                    quotation.getItems().size(), items.size());

            // Get PR to check which items had quantity changes
            PurchaseRequestEntity prEntity = purchaseRequestRepository.findById(dto.getPurchaseRequestId())
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found"));

            // Create maps for efficient lookup
            Map<String, VendorQuotationItemEntity> existingItemsMap = quotation.getItems().stream()
                    .collect(Collectors.toMap(
                            VendorQuotationItemEntity::getUniqueCode,
                            item -> item,
                            (existing, replacement) -> existing));

            Map<String, VendorQuotationItemEntity> newItemsMap = items.stream()
                    .collect(Collectors.toMap(
                            VendorQuotationItemEntity::getUniqueCode,
                            item -> item,
                            (existing, replacement) -> existing));

            Map<String, PurchaseRequestItemEntity> prItemsMap = prEntity.getLineItems().stream()
                    .collect(Collectors.toMap(
                            PurchaseRequestItemEntity::getUniqueCode,
                            item -> item,
                            (existing, replacement) -> existing));

            // Process each existing item
            List<VendorQuotationItemEntity> updatedItems = new ArrayList<>();
            for (VendorQuotationItemEntity existingItem : quotation.getItems()) {
                String uniqueCode = existingItem.getUniqueCode();
                PurchaseRequestItemEntity prItem = prItemsMap.get(uniqueCode);
                VendorQuotationItemEntity newItem = newItemsMap.get(uniqueCode);

                // Check if this item had quantity changes
                boolean itemQuantityChanged = prItem != null && Boolean.TRUE.equals(prItem.getQuantityUpdated());
                // Check if this is a reshare (allow updates for reshared tokens)
                boolean isReshare = tokenEntity != null && Boolean.TRUE.equals(tokenEntity.getIsReshared());

                if ((itemQuantityChanged || isReshare) && newItem != null) {
                    // Update with new data for items that had quantity changes or regular reshares
                    existingItem.setAvailableQuantity(newItem.getAvailableQuantity());
                    existingItem.setUnitPrice(newItem.getUnitPrice());
                    existingItem.setDeliveryDate(newItem.getDeliveryDate());
                    existingItem.setTotalCost(newItem.getTotalCost());
                    existingItem.setVendorRemarks(newItem.getVendorRemarks());
                    logger.debug("Updated existing item {}: {} with new data",
                            existingItem.getId(), uniqueCode);
                } else {
                    // Keep existing data for items without quantity changes
                    logger.debug("Preserved existing item {}: {} (no quantity change)",
                            existingItem.getId(), uniqueCode);
                }
                updatedItems.add(existingItem);
            }

            // Add any completely new items (shouldn't happen in normal flow but handle it)
            for (VendorQuotationItemEntity newItem : items) {
                if (!existingItemsMap.containsKey(newItem.getUniqueCode())) {
                    newItem.setVendorQuotation(quotation);
                    updatedItems.add(newItem);
                    logger.debug("Added completely new item: {}", newItem.getUniqueCode());
                }
            }

            // Update the quotation's items collection
            quotation.getItems().clear();
            quotation.getItems().addAll(updatedItems);

            // Update additional charges
            quotation.getAdditionalCharges().clear();
            quotation.getAdditionalCharges().addAll(charges);
        } else {
            // For new quotations, just set the items and charges
            quotation.setItems(items);
            quotation.setAdditionalCharges(charges);
        }

        // Calculate totals
        double lineItemsTotal = quotation.getItems().stream()
                .mapToDouble(VendorQuotationItemEntity::getTotalCost)
                .sum();
        double additionalChargesTotal = quotation.getAdditionalCharges().stream()
                .mapToDouble(VendorQuotationChargeEntity::getAmount)
                .sum();
        double grandTotal = lineItemsTotal + additionalChargesTotal;

        quotation.setTotalCost(lineItemsTotal);
        quotation.setAdditionalChargesTotal(additionalChargesTotal);
        quotation.setGrandTotal(grandTotal);
        logger.info("Quotation grand total calculated: {}", grandTotal);

        logger.info("Saving quotation to database...");
        VendorQuotationEntity savedQuotation = vendorQuotationRepository.save(quotation);
        logger.info("Quotation saved successfully with ID: {}", savedQuotation.getId());

        // Mark the token as used if it was provided
        if (tokenEntity != null) {
            logger.info("Marking token {} as used for quotation {}", token, savedQuotation.getId());
            tokenService.markTokenAsUsed(token, savedQuotation.getId());
            if (isUpdate) {
                logger.info("Marked token {} as used for UPDATED quotation {}", token, savedQuotation.getId());
            } else {
                logger.info("Marked token {} as used for NEW quotation {}", token, savedQuotation.getId());
            }
        }

        logger.info("Converting quotation {} to DTO for response", savedQuotation.getId());
        VendorQuotationDTO result = convertToDTO(savedQuotation);
        logger.info("Quotation submission completed successfully for PR {} and vendor {}",
                dto.getPurchaseRequestId(), dto.getVendorId());
        return result;
    }

    @Transactional
    public VendorQuotationDTO selectQuotation(Long quotationId) {
        VendorQuotationEntity quotation = vendorQuotationRepository.findById(quotationId)
                .orElseThrow(() -> new IllegalArgumentException("Quotation not found"));

        // Get all quotations for this PR
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository
                .findByPurchaseRequestId(quotation.getPurchaseRequest().getId());

        // First, unselect all items in all quotations
        allQuotations.forEach(q -> {
            q.getItems().forEach(item -> item.setSelected(false));
            vendorQuotationRepository.save(q);
        });

        // Then select all items in the chosen quotation
        quotation.getItems().forEach(item -> {
            item.setSelected(true);
            vendorQuotationRepository.save(quotation);
        });

        // AUTOMATICALLY UPDATE PR LINE ITEMS WITH SELECTED QUOTATION RATES
        updatePRItemsWithSelectedQuotationRates(quotation.getPurchaseRequest().getId());

        return convertToDTO(quotation);
    }

    @Transactional
    public VendorQuotationDTO selectQuotationItems(Long quotationId, List<String> selectedItemCodes,
            List<Long> selectedItemIds) {
        try {
            logger.info("Starting selection process for quotation {} with codes {} and IDs {}",
                    quotationId, selectedItemCodes, selectedItemIds);

            // Get the quotation being selected from
            VendorQuotationEntity selectedQuotation = vendorQuotationRepository.findById(quotationId)
                    .orElseThrow(() -> new IllegalArgumentException("Quotation not found with ID: " + quotationId));

            // Get all quotations for this PR
            List<VendorQuotationEntity> allQuotations = vendorQuotationRepository
                    .findByPurchaseRequestId(selectedQuotation.getPurchaseRequest().getId());

            // First, unselect all items
            allQuotations.forEach(quotation -> {
                quotation.getItems().forEach(item -> item.setSelected(false));
                vendorQuotationRepository.save(quotation);
            });

            // Create a map to track which uniqueCodes have been selected
            Set<String> selectedUniqueCodes = new HashSet<>();

            // Select items by codes
            if (selectedItemCodes != null) {
                selectedQuotation.getItems().forEach(item -> {
                    if (selectedItemCodes.contains(item.getUniqueCode()) &&
                            !selectedUniqueCodes.contains(item.getUniqueCode())) {
                        item.setSelected(true);
                        selectedUniqueCodes.add(item.getUniqueCode());
                    }
                });
            }

            // Select items by IDs
            if (selectedItemIds != null) {
                selectedQuotation.getItems().forEach(item -> {
                    if (selectedItemIds.contains(item.getId()) &&
                            !selectedUniqueCodes.contains(item.getUniqueCode())) {
                        item.setSelected(true);
                        selectedUniqueCodes.add(item.getUniqueCode());
                    }
                });
            }

            vendorQuotationRepository.save(selectedQuotation);

            // AUTOMATICALLY UPDATE PR LINE ITEMS WITH SELECTED QUOTATION RATES
            updatePRItemsWithSelectedQuotationRates(selectedQuotation.getPurchaseRequest().getId());

            return convertToDTO(selectedQuotation);
        } catch (Exception e) {
            logger.error("Error in selectQuotationItems", e);
            throw e;
        }
    }

    @Transactional
    public List<VendorQuotationDTO> selectItemsForPR(Long prId, VendorQuotationSelectionDTO selectionDTO) {
        try {
            logger.info("Starting PR-level selection process for PR {} with vendor selections {}",
                    prId, selectionDTO.getVendorSelections());

            // Get all quotations for this PR
            List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);
            if (allQuotations.isEmpty()) {
                throw new IllegalArgumentException("No quotations found for PR: " + prId);
            }

            // First, unselect all items
            allQuotations.forEach(quotation -> {
                quotation.getItems().stream()
                        .filter(item -> item.getUnitPrice() != null)
                        .forEach(item -> item.setSelected(false));
                vendorQuotationRepository.save(quotation);
            });

            // Create a map to track which uniqueCodes have been selected
            Set<String> selectedUniqueCodes = new HashSet<>();

            // Process each vendor's selections
            if (selectionDTO.getVendorSelections() != null) {
                selectionDTO.getVendorSelections().forEach((vendorId, uniqueCodes) -> {
                    // Find the quotation for this vendor
                    allQuotations.stream()
                            .filter(q -> q.getVendor().getSrNo().equals(vendorId))
                            .findFirst()
                            .ifPresent(quotation -> {
                                // Select the items for this vendor
                                quotation.getItems().forEach(item -> {
                                    if (item.getUnitPrice() != null &&
                                            uniqueCodes.contains(item.getUniqueCode()) &&
                                            !selectedUniqueCodes.contains(item.getUniqueCode())) {
                                        item.setSelected(true);
                                        selectedUniqueCodes.add(item.getUniqueCode());
                                    }
                                });

                                vendorQuotationRepository.save(quotation);
                            });
                });
            }

            // Update the PR status to PENDING_QUANTITY_APPROVAL
            PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

            logger.info("PR {} current status: {}", prId, pr.getStatus());

            // Update status if it's currently PENDING_VENDOR_SELECTION or PENDING_APPROVAL
            if ("PENDING_VENDOR_SELECTION".equals(pr.getStatus()) || "PENDING_APPROVAL".equals(pr.getStatus())) {
                String oldStatus = pr.getStatus();
                pr.setStatus("PENDING_QUANTITY_APPROVAL");

                // Update PR currency from selected quotations
                String prCurrency = getSelectedQuotationsCurrency(allQuotations);
                if (prCurrency != null) {
                    pr.setCurrency(prCurrency);
                    logger.info("PR {} currency updated to: {}", prId, prCurrency);
                }

                purchaseRequestRepository.save(pr);
                logger.info("PR {} status changed from {} to PENDING_QUANTITY_APPROVAL", prId, oldStatus);
            } else {
                logger.warn("PR {} status is {} - not updating to PENDING_QUANTITY_APPROVAL", prId, pr.getStatus());
            }

            // AUTOMATICALLY UPDATE PR LINE ITEMS WITH SELECTED QUOTATION RATES
            updatePRItemsWithSelectedQuotationRates(prId);

            // Return all updated quotations
            return allQuotations.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error in selectItemsForPR", e);
            throw e;
        }
    }

    public List<VendorQuotationDTO> getQuotationsForPR(Long prId) {
        try {
            logger.info("Getting quotations for PR {}", prId);

            // Get the purchase request to access all associated vendors
            PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

            // Get existing quotations for this PR
            List<VendorQuotationEntity> existingQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

            // Create a map of existing quotations by vendor ID for quick lookup
            Map<Long, VendorQuotationEntity> quotationsByVendorId = existingQuotations.stream()
                    .collect(Collectors.toMap(
                            q -> q.getVendor().getSrNo(),
                            q -> q
                    ));

            // Initialize null selections to false for existing quotations
            existingQuotations.forEach(quotation -> quotation.getItems().forEach(item -> {
                if (item.getSelected() == null) {
                    item.setSelected(false);
                    vendorQuotationItemRepository.save(item);
                }
            }));

            // Create result list including all vendors (with and without quotations)
            List<VendorQuotationDTO> result = new ArrayList<>();

            for (VendorEntity vendor : pr.getVendors()) {
                VendorQuotationEntity existingQuotation = quotationsByVendorId.get(vendor.getSrNo());

                if (existingQuotation != null) {
                    // Vendor has submitted quotation - return actual data
                    result.add(convertToDTO(existingQuotation));
                } else {
                    // Vendor hasn't submitted quotation - return placeholder with line items but "NA" values
                    VendorQuotationDTO placeholderDTO = new VendorQuotationDTO();
                    placeholderDTO.setId(null);
                    placeholderDTO.setPurchaseRequestId(prId);
                    placeholderDTO.setVendorId(vendor.getSrNo());
                    placeholderDTO.setVendorName(vendor.getVendorName());
                    placeholderDTO.setVendorCompanyName(vendor.getCompanyName());
                    placeholderDTO.setSubmissionDate(null);
                    placeholderDTO.setTotalCost(0.0);
                    placeholderDTO.setStatus("NA"); // Status indicating no quotation submitted
                    placeholderDTO.setPriority(pr.getPriority());

                    // Create line items from PR with "NA" values for pricing
                    List<VendorQuotationItemDTO> placeholderItems = pr.getLineItems().stream()
                            .map(prItem -> {
                                VendorQuotationItemDTO itemDTO = new VendorQuotationItemDTO();
                                itemDTO.setId(null);
                                itemDTO.setUniqueCode(prItem.getUniqueCode());
                                itemDTO.setProductName(prItem.getProductName());
                                itemDTO.setAvailableQuantity(null); // NA - vendor hasn't provided
                                itemDTO.setUnitPrice(null); // NA - vendor hasn't provided
                                itemDTO.setDeliveryDate(null); // NA - vendor hasn't provided
                                itemDTO.setTotalCost(null); // NA - vendor hasn't provided
                                itemDTO.setSelected(false);

                                // Set original quantity from PR
                                itemDTO.setOriginalQuantity(prItem.getQuantity());

                                // Set changing quantity if available
                                itemDTO.setChangingQuantity(prItem.getChangeInQuantity());

                                // Set material family
                                itemDTO.setMaterialFamily(prItem.getMaterialFamily());

                                return itemDTO;
                            })
                            .collect(Collectors.toList());

                    placeholderDTO.setItems(placeholderItems);
                    result.add(placeholderDTO);
                }
            }

            logger.info("Returning {} vendor quotations for PR {} (including {} submitted and {} pending)",
                    result.size(), prId, existingQuotations.size(), result.size() - existingQuotations.size());

            return result;
        } catch (Exception e) {
            logger.error("Error in getQuotationsForPR for PR {}", prId, e);
            throw new RuntimeException("Failed to fetch quotations for PR " + prId, e);
        }
    }

    public List<VendorQuotationDTO> getSelectedItemsForPR(Long prId) {
        List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

        return allQuotations.stream()
                .map(quotation -> {
                    // Filter only selected items
                    List<VendorQuotationItemEntity> selectedItems = quotation.getItems().stream()
                            .filter(VendorQuotationItemEntity::getSelected)
                            .collect(Collectors.toList());

                    if (selectedItems.isEmpty()) {
                        return null; // skip vendors with no selected items
                    }

                    VendorQuotationEntity filteredQuotation = new VendorQuotationEntity();
                    filteredQuotation.setId(quotation.getId());
                    filteredQuotation.setVendor(quotation.getVendor());
                    filteredQuotation.setPurchaseRequest(quotation.getPurchaseRequest());
                    filteredQuotation.setSubmissionDate(quotation.getSubmissionDate());
                    filteredQuotation.setStatus(quotation.getStatus());
                    filteredQuotation.setItems(selectedItems);

                    // Optional: recalculate total cost for only selected items
                    filteredQuotation.setTotalCost(
                            selectedItems.stream().mapToDouble(VendorQuotationItemEntity::getTotalCost).sum());

                    return convertToDTO(filteredQuotation);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private VendorQuotationDTO convertToDTO(VendorQuotationEntity entity) {
        VendorQuotationDTO dto = new VendorQuotationDTO();
        dto.setId(entity.getId());
        dto.setPurchaseRequestId(entity.getPurchaseRequest().getId());
        dto.setVendorId(entity.getVendor().getSrNo());
        dto.setVendorName(entity.getVendor().getVendorName());
        dto.setVendorCompanyName(entity.getVendor().getCompanyName());
        dto.setSubmissionDate(entity.getSubmissionDate());
        dto.setTotalCost(entity.getTotalCost());
        dto.setPriority(entity.getPurchaseRequest().getPriority());
        dto.setCurrency(entity.getCurrency());
        dto.setStockPoint(entity.getStockPoint());
        dto.setReferenceNumber(entity.getReferenceNumber());
        dto.setAdditionalChargesTotal(entity.getAdditionalChargesTotal());
        dto.setGrandTotal(entity.getGrandTotal());

        // Create maps for efficient lookup of original quantities, changing quantities, and material families
        Map<String, Integer> originalQuantityMap = entity.getPurchaseRequest().getLineItems().stream()
                .collect(Collectors.toMap(
                        PurchaseRequestItemEntity::getUniqueCode,
                        PurchaseRequestItemEntity::getQuantity,
                        (existing, replacement) -> existing // Keep first if duplicates
                ));

        Map<String, Integer> changingQuantityMap = entity.getPurchaseRequest().getLineItems().stream()
                .collect(Collectors.toMap(
                        PurchaseRequestItemEntity::getUniqueCode,
                        item -> item.getChangeInQuantity() != null ? item.getChangeInQuantity() : 0,
                        (existing, replacement) -> existing // Keep first if duplicates
                ));

        // Get all unique codes for material family lookup
        Set<String> uniqueCodes = entity.getItems().stream()
                .map(VendorQuotationItemEntity::getUniqueCode)
                .collect(Collectors.toSet());

        // Batch lookup material families by unique codes
        Map<String, String> materialFamilyMap = new HashMap<>();
        if (!uniqueCodes.isEmpty()) {
            for (String uniqueCode : uniqueCodes) {
                List<ShipbuildersItemEntity> shipbuildersItems = shipbuildersItemRepository
                        .searchByItemCode(uniqueCode);
                if (!shipbuildersItems.isEmpty()) {
                    // Use the first match's subCategory as material family
                    materialFamilyMap.put(uniqueCode, shipbuildersItems.get(0).getSubCategory());
                }
            }
        }

        dto.setItems(entity.getItems().stream()
                .map(item -> {
                    VendorQuotationItemDTO itemDTO = new VendorQuotationItemDTO();
                    itemDTO.setId(item.getId());
                    itemDTO.setUniqueCode(item.getUniqueCode());
                    itemDTO.setProductName(item.getProductName());
                    itemDTO.setAvailableQuantity(item.getAvailableQuantity());
                    itemDTO.setUnitPrice(item.getUnitPrice());
                    itemDTO.setDeliveryDate(item.getDeliveryDate());
                    itemDTO.setTotalCost(item.getTotalCost());
                    itemDTO.setSelected(item.getSelected());

                    // Set original quantity from PR line items
                    itemDTO.setOriginalQuantity(originalQuantityMap.get(item.getUniqueCode()));

                    // Set changing quantity from PR line items (engineering department changes)
                    itemDTO.setChangingQuantity(changingQuantityMap.get(item.getUniqueCode()));

                    // Set material family from ShipbuildersItemEntity using uniqueCode
                    itemDTO.setMaterialFamily(materialFamilyMap.get(item.getUniqueCode()));

                    // Set vendor remarks for individual items
                    itemDTO.setVendorRemarks(item.getVendorRemarks());

                    return itemDTO;
                })
                .collect(Collectors.toList()));

        // Convert additional charges
        if (entity.getAdditionalCharges() != null) {
            dto.setAdditionalCharges(entity.getAdditionalCharges().stream()
                    .map(charge -> {
                        VendorQuotationChargeDTO chargeDTO = new VendorQuotationChargeDTO();
                        chargeDTO.setId(charge.getId());
                        chargeDTO.setDescription(charge.getDescription());
                        chargeDTO.setAmount(charge.getAmount());
                        return chargeDTO;
                    })
                    .collect(Collectors.toList()));
        }

        return dto;
    }

    /**
     * Update PR line items with rates from selected quotations
     * This method is called automatically when quotations are selected
     */
    @Transactional
    private void updatePRItemsWithSelectedQuotationRates(Long prId) {
        try {
            PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with id: " + prId));

            // Get all quotations for this PR
            List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(prId);

            if (allQuotations.isEmpty()) {
                logger.warn("No quotations found for PR {}", prId);
                return;
            }

            // Get all selected quotation items
            List<VendorQuotationItemEntity> selectedItems = allQuotations.stream()
                    .flatMap(quotation -> quotation.getItems().stream())
                    .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                    .collect(Collectors.toList());

            if (selectedItems.isEmpty()) {
                logger.warn("No selected quotation items found for PR {}", prId);
                return;
            }

            logger.info("Found {} selected quotation items for PR {}", selectedItems.size(), prId);

            // Update PR items with rates from selected quotation items
            for (PurchaseRequestItemEntity prItem : pr.getLineItems()) {
                // Find matching selected quotation item by uniqueCode
                selectedItems.stream()
                        .filter(quotationItem -> quotationItem.getUniqueCode().equals(prItem.getUniqueCode()))
                        .findFirst()
                        .ifPresent(selectedItem -> {
                            // Update rate and total
                            BigDecimal rate = BigDecimal.valueOf(selectedItem.getUnitPrice());
                            prItem.setRate(rate);

                            // Calculate total (quantity * rate)
                            BigDecimal quantity = new BigDecimal(prItem.getQuantity());
                            BigDecimal total = quantity.multiply(rate);
                            prItem.setTotal(total);

                            logger.info("Updated PR item {} with rate {} from selected quotation",
                                    prItem.getProductName(), rate);
                        });
            }

            // Save the updated PR
            purchaseRequestRepository.save(pr);
            logger.info("Successfully updated PR {} with selected quotation rates", prId);

        } catch (Exception e) {
            logger.error("Error updating PR {} with selected quotation rates: {}", prId, e.getMessage(), e);
        }
    }

    /**
     * Validate bidding token and return PR details for the bidding form
     * Enhanced to support partial quantity change behavior - items with unchanged
     * quantities
     * are marked as read-only and pre-filled with existing quotation data
     */
    public Map<String, Object> validateTokenAndGetPRDetails(String token) {
        // Validate the token
        Optional<VendorBiddingTokenEntity> tokenOpt = tokenService.validateToken(token);
        if (tokenOpt.isEmpty()) {
            throw new SecurityException(
                    "Invalid or expired bidding token. This link may have already been used or is no longer valid.");
        }

        VendorBiddingTokenEntity tokenEntity = tokenOpt.get();
        PurchaseRequestEntity pr = tokenEntity.getPurchaseRequest();
        VendorEntity vendor = tokenEntity.getVendor();

        // Check if vendor has already submitted a quotation for this PR
        List<VendorQuotationEntity> existingQuotations = vendorQuotationRepository
                .findByPurchaseRequestIdAndVendorSrNo(pr.getId(), vendor.getSrNo());

        // If quotation exists, this is an update scenario (reshared link)
        boolean isUpdateScenario = !existingQuotations.isEmpty();
        if (isUpdateScenario) {
            logger.info("Token {} is for updating existing quotation for PR {} and vendor {}",
                    token, pr.getId(), vendor.getSrNo());
        }

        // Prepare response data
        Map<String, Object> result = new HashMap<>();

        // Purchase Request basic info
        Map<String, Object> prInfo = new HashMap<>();
        prInfo.put("id", pr.getId());
        prInfo.put("prId", pr.getPrId());
        prInfo.put("contractorName", pr.getContractorName());
        prInfo.put("yardNumber", pr.getYardNumber());
        prInfo.put("projectName", pr.getProjectName());
        prInfo.put("createdDate", pr.getCreatedDate());
        prInfo.put("status", pr.getStatus());
        prInfo.put("quantityUpdated", pr.getQuantityUpdated()); // Add quantity change flag
        prInfo.put("priority", pr.getPriority());

        // Vendor info
        Map<String, Object> vendorInfo = new HashMap<>();
        vendorInfo.put("id", vendor.getSrNo());
        vendorInfo.put("companyName", vendor.getCompanyName());
        vendorInfo.put("vendorName", vendor.getVendorName());
        vendorInfo.put("emailId", vendor.getEmailId());

        // Create a map of existing quotation items by uniqueCode for quick lookup
        final Map<String, VendorQuotationItemEntity> existingItemsMap;
        if (isUpdateScenario) {
            VendorQuotationEntity existingQuotation = existingQuotations.get(0);
            existingItemsMap = existingQuotation.getItems().stream()
                    .collect(Collectors.toMap(
                            VendorQuotationItemEntity::getUniqueCode,
                            item -> item,
                            (existing, replacement) -> existing // Keep first if duplicates
                    ));
        } else {
            existingItemsMap = new HashMap<>();
        }

        // Enhanced line items with editability and existing data merged
        List<Map<String, Object>> enhancedLineItems = pr.getLineItems().stream()
                .map(prItem -> {
                    Map<String, Object> itemMap = new HashMap<>();

                    // Basic PR item data
                    itemMap.put("uniqueCode", prItem.getUniqueCode());
                    itemMap.put("productName", prItem.getProductName());
                    itemMap.put("quantity", prItem.getQuantity());
                    itemMap.put("unitOfMeasure", prItem.getUnitOfMeasure());
                    itemMap.put("itemName", prItem.getItemName());
                    itemMap.put("changeInQuantity", prItem.getChangeInQuantity());
                    itemMap.put("quantityUpdated", prItem.getQuantityUpdated());
                    itemMap.put("remarks", prItem.getRemarks());
                    itemMap.put("attachments", prItem.getAttachments());

                    // Determine if this item should be editable based on scenario
                    boolean isEditable = true; // Default for new quotations
                    if (isUpdateScenario) {
                        // For vendor edit forms (reshared tokens), all items should be editable
                        // For regular re-quotations, only allow editing if quantity was changed
                        if (Boolean.TRUE.equals(tokenEntity.getIsReshared())) {
                            isEditable = true; // All items editable for vendor edit forms
                        } else {
                            isEditable = Boolean.TRUE.equals(prItem.getQuantityUpdated());
                        }
                    }
                    itemMap.put("isEditable", isEditable);

                    // Add existing quotation data if available
                    VendorQuotationItemEntity existingItem = existingItemsMap.get(prItem.getUniqueCode());
                    if (existingItem != null) {
                        // For vendor edit forms, always provide existing data as pre-fill values
                        if (Boolean.TRUE.equals(tokenEntity.getIsReshared())) {
                            itemMap.put("existingAvailableQuantity", existingItem.getAvailableQuantity());
                            itemMap.put("existingUnitPrice", existingItem.getUnitPrice());
                            itemMap.put("existingDeliveryDate", existingItem.getDeliveryDate());
                            itemMap.put("existingTotalCost", existingItem.getTotalCost());
                            itemMap.put("existingVendorRemarks", existingItem.getVendorRemarks());
                        } else if (!isEditable) {
                            // For regular re-quotations, read-only items get existing data
                            itemMap.put("existingAvailableQuantity", existingItem.getAvailableQuantity());
                            itemMap.put("existingUnitPrice", existingItem.getUnitPrice());
                            itemMap.put("existingDeliveryDate", existingItem.getDeliveryDate());
                            itemMap.put("existingTotalCost", existingItem.getTotalCost());
                            itemMap.put("existingVendorRemarks", existingItem.getVendorRemarks());
                        } else {
                            // For regular re-quotations, editable items get default values
                            itemMap.put("defaultAvailableQuantity", existingItem.getAvailableQuantity());
                            itemMap.put("defaultUnitPrice", existingItem.getUnitPrice());
                            itemMap.put("defaultDeliveryDate", existingItem.getDeliveryDate());
                            itemMap.put("defaultTotalCost", existingItem.getTotalCost());
                            itemMap.put("defaultVendorRemarks", existingItem.getVendorRemarks());
                        }
                        itemMap.put("hasExistingData", true);
                    } else {
                        itemMap.put("hasExistingData", false);
                    }

                    return itemMap;
                })
                .collect(Collectors.toList());

        result.put("purchaseRequest", prInfo);
        result.put("vendor", vendorInfo);
        result.put("lineItems", enhancedLineItems);
        result.put("token", token); // Include token for form submission
        result.put("isUpdate", isUpdateScenario); // Indicate if this is an update
        result.put("isReshared", tokenEntity.getIsReshared()); // Flag to indicate if this token was generated from a reshare

        // Add revised grand total information if available (for vendor edit form)
        if (tokenEntity.getRevisedGrandTotal() != null) {
            result.put("targetRevisedTotal", tokenEntity.getRevisedGrandTotal());

            // Calculate discount information if we have both original and target totals
            if (isUpdateScenario) {
                VendorQuotationEntity existingQuotation = existingQuotations.get(0);
                Double originalTotal = existingQuotation.getGrandTotal();
                Double targetTotal = tokenEntity.getRevisedGrandTotal();

                if (originalTotal != null && targetTotal != null) {
                    Double discountTarget = originalTotal - targetTotal;
                    result.put("discountTarget", discountTarget);

                    if (originalTotal > 0) {
                        Double discountPercentage = (discountTarget / originalTotal) * 100;
                        result.put("discountPercentage", discountPercentage);
                    }
                }
            }
        }

        // Add existing quotation global fields if update scenario
        if (isUpdateScenario) {
            VendorQuotationEntity existingQuotation = existingQuotations.get(0);
            result.put("existingCurrency", existingQuotation.getCurrency());
            result.put("existingStockPoint", existingQuotation.getStockPoint());
            result.put("existingReferenceNumber", existingQuotation.getReferenceNumber());

            // Add existing additional charges
            if (existingQuotation.getAdditionalCharges() != null) {
                List<Map<String, Object>> existingCharges = existingQuotation.getAdditionalCharges().stream()
                        .map(charge -> {
                            Map<String, Object> chargeMap = new HashMap<>();
                            chargeMap.put("id", charge.getId());
                            chargeMap.put("description", charge.getDescription());
                            chargeMap.put("amount", charge.getAmount());
                            return chargeMap;
                        })
                        .collect(Collectors.toList());
                result.put("existingAdditionalCharges", existingCharges);
            }

            result.put("existingAdditionalChargesTotal", existingQuotation.getAdditionalChargesTotal());
            result.put("existingGrandTotal", existingQuotation.getGrandTotal());
        }

        // Add summary information for the frontend
        if (isUpdateScenario) {
            long editableItemsCount = enhancedLineItems.stream()
                    .mapToLong(item -> Boolean.TRUE.equals(item.get("isEditable")) ? 1 : 0)
                    .sum();
            long readOnlyItemsCount = enhancedLineItems.size() - editableItemsCount;

            result.put("editableItemsCount", editableItemsCount);
            result.put("readOnlyItemsCount", readOnlyItemsCount);
            result.put("hasQuantityChanges", editableItemsCount > 0);

            logger.info(
                    "Validated token {} for UPDATING quotation for PR {} and vendor {} - {} editable items, {} read-only items",
                    token, pr.getId(), vendor.getSrNo(), editableItemsCount, readOnlyItemsCount);
        } else {
            result.put("editableItemsCount", enhancedLineItems.size());
            result.put("readOnlyItemsCount", 0);
            result.put("hasQuantityChanges", false);

            logger.info("Validated token {} for NEW quotation for PR {} and vendor {} - {} total items",
                    token, pr.getId(), vendor.getSrNo(), enhancedLineItems.size());
        }

        return result;
    }

    /**
     * Get currency from selected quotations - uses the first selected quotation's currency
     */
    private String getSelectedQuotationsCurrency(List<VendorQuotationEntity> quotations) {
        return quotations.stream()
                .filter(quotation -> quotation.getItems().stream()
                        .anyMatch(item -> Boolean.TRUE.equals(item.getSelected())))
                .findFirst()
                .map(VendorQuotationEntity::getCurrency)
                .orElse(null);
    }
}
