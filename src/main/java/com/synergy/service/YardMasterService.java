package com.synergy.service;

import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.entity.YardMasterEntity;
import com.synergy.dto.YardMasterDTO;
import com.synergy.dto.YardMasterDropdownDTO;
import com.synergy.repository.YardMasterRepository;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class YardMasterService {

    @Autowired
    private YardMasterRepository yardMasterRepository;

    /**
     * Get all yards
     */
    public List<YardMasterDTO> getAllYards() {
        return yardMasterRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get yard by ID
     */
    public Optional<YardMasterDTO> getYardById(Long id) {
        return yardMasterRepository.findById(id)
                .map(this::convertToDTO);
    }

    /**
     * Get yard by yard number
     */
    public Optional<YardMasterDTO> getYardByYardNumber(String yardNumber) {
        return yardMasterRepository.findByYardNumber(yardNumber)
                .map(this::convertToDTO);
    }

    /**
     * Create new yard
     */
    public YardMasterDTO createYard(YardMasterDTO yardDTO) {
        // Validate yard number uniqueness
        if (yardMasterRepository.existsByYardNumber(yardDTO.getYardNumber())) {
            throw new IllegalArgumentException("Yard number already exists: " + yardDTO.getYardNumber());
        }

        YardMasterEntity entity = convertToEntity(yardDTO);
        YardMasterEntity savedEntity = yardMasterRepository.save(entity);
        return convertToDTO(savedEntity);
    }

    /**
     * Update existing yard
     */
    @Transactional
    public YardMasterDTO updateYard(Long id, YardMasterDTO yardDTO) {
        Optional<YardMasterEntity> existingYardOpt = yardMasterRepository.findById(id);
        if (existingYardOpt.isEmpty()) {
            throw new IllegalArgumentException("Yard not found with ID: " + id);
        }

        // Validate yard number uniqueness (excluding current record)
        if (yardMasterRepository.existsByYardNumberAndIdNot(yardDTO.getYardNumber(), id)) {
            throw new IllegalArgumentException("Yard number already exists: " + yardDTO.getYardNumber());
        }

        YardMasterEntity existingYard = existingYardOpt.get();
        existingYard.setYardNumber(yardDTO.getYardNumber());
        existingYard.setClientName(yardDTO.getClientName());
        existingYard.setProjectName(yardDTO.getProjectName());

        YardMasterEntity savedEntity = yardMasterRepository.save(existingYard);
        return convertToDTO(savedEntity);
    }

    /**
     * Delete yard (hard delete)
     */
    public void deleteYard(Long id) {
        Optional<YardMasterEntity> yardOpt = yardMasterRepository.findById(id);
        if (yardOpt.isEmpty()) {
            throw new IllegalArgumentException("Yard not found with ID: " + id);
        }

        yardMasterRepository.deleteById(id);
    }

    /**
     * Get yards for dropdown
     */
    public List<YardMasterDropdownDTO> getYardsForDropdown() {
        return yardMasterRepository.findAllForDropdown();
    }

    /**
     * Search yards by criteria
     */
    public List<YardMasterDTO> searchYards(String yardNumber, String clientName, String projectName) {
        return yardMasterRepository.searchYards(yardNumber, clientName, projectName).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public void exportYardMasterToExcel(List<YardMasterEntity> items, HttpServletResponse response) throws DocumentException, IOException {

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=yard_master.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Yard_Master Items");

        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setWrapText(true);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        String[] headers = {
                "id", "Client Name", "Project Name", "Yard Number", "Created Date", "Updated Date"
        };

        CellStyle dateStyle = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));

        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(30);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // Fill data rows
        int rowNum = 1;
        for (YardMasterEntity item : items) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(getLong(item.getId()));
            row.createCell(1).setCellValue(nullValue(item.getClientName()));
            row.createCell(2).setCellValue(nullValue(item.getProjectName()));
            row.createCell(3).setCellValue(nullValue(item.getYardNumber()));

            Cell createdCell = row.createCell(4);
            if (item.getCreatedDate() != null) {
                createdCell.setCellValue(item.getCreatedDate());
                createdCell.setCellStyle(dateStyle);
            } else {
                createdCell.setCellValue("");
            }

            Cell updatedCell = row.createCell(5);
            if (item.getUpdatedDate() != null) {
                updatedCell.setCellValue(item.getUpdatedDate());
                updatedCell.setCellStyle(dateStyle);
            } else {
                updatedCell.setCellValue("");
            }
        }

        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Helper methods for excel
    private String nullValue(String value) {
        return value == null ? "" : value;
    }

    private long getLong(Long value) {
        return value == null ? 0L : value;
    }


    /**
     * Convert entity to DTO
     */
    private YardMasterDTO convertToDTO(YardMasterEntity entity) {
        YardMasterDTO dto = new YardMasterDTO();
        dto.setId(entity.getId());
        dto.setYardNumber(entity.getYardNumber());
        dto.setClientName(entity.getClientName());
        dto.setProjectName(entity.getProjectName());
        dto.setCreatedDate(entity.getCreatedDate());
        dto.setUpdatedDate(entity.getUpdatedDate());
        return dto;
    }

    /**
     * Convert DTO to entity
     */
    private YardMasterEntity convertToEntity(YardMasterDTO dto) {
        YardMasterEntity entity = new YardMasterEntity();
        entity.setId(dto.getId());
        entity.setYardNumber(dto.getYardNumber());
        entity.setClientName(dto.getClientName());
        entity.setProjectName(dto.getProjectName());
        return entity;
    }

    public void exportYardMasterToPdf(List<YardMasterEntity> items, HttpServletResponse response) throws DocumentException, IOException {

        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=yard_master.pdf");

        PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Yard Master", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        PdfPTable table = new PdfPTable(new float[]{1, 2, 2, 2, 2, 2});
        table.setWidthPercentage(100);

        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {
                "ID", "Client Name", "Project Name", "Yard Number", "Created Date", "Updated Date"
        };

        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        for (YardMasterEntity yardMaster : items) {
            table.addCell(new Phrase(String.valueOf(yardMaster.getId()), dataFont));
            table.addCell(new Phrase(yardMaster.getClientName() != null ? yardMaster.getClientName() : "", dataFont));
            table.addCell(new Phrase(yardMaster.getProjectName() != null ? yardMaster.getProjectName() : "", dataFont));
            table.addCell(new Phrase(yardMaster.getYardNumber() != null ? yardMaster.getYardNumber() : "", dataFont));
            table.addCell(new Phrase(yardMaster.getCreatedDate() != null ? sdf.format(yardMaster.getCreatedDate()) : "", dataFont));
            table.addCell(new Phrase(yardMaster.getUpdatedDate() != null ? sdf.format(yardMaster.getUpdatedDate()) : "", dataFont));
        }

        document.add(table);
        document.close();
    }
}
