package com.synergy.service;

import com.synergy.dto.PurchaseOrderDeliveryTermsDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.entity.VendorPODeliveryTermsEntity;
import com.synergy.repository.VendorPODeliveryTermsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class VendorPODeliveryTermsService {

    @Autowired
    private VendorPODeliveryTermsRepository deliveryTermsRepository;

    /**
     * Save or update delivery terms for a vendor PO when PDF is generated
     */
    public VendorPODeliveryTermsEntity saveDeliveryTerms(String vendorPoId, Long originalPrId, Long vendorId, 
                                                        PurchaseOrderDeliveryTermsDTO deliveryTermsDTO) {
        // Check if delivery terms already exist for this vendor PO
        Optional<VendorPODeliveryTermsEntity> existingTerms = deliveryTermsRepository.findByVendorPoId(vendorPoId);
        
        VendorPODeliveryTermsEntity deliveryTerms;
        if (existingTerms.isPresent()) {
            // Update existing terms
            deliveryTerms = existingTerms.get();
        } else {
            // Create new terms
            deliveryTerms = new VendorPODeliveryTermsEntity();
            deliveryTerms.setVendorPoId(vendorPoId);
            deliveryTerms.setOriginalPrId(originalPrId);
            deliveryTerms.setVendorId(vendorId);
        }
        
        // Update fields from DTO
        deliveryTerms.setCurrency(deliveryTermsDTO.getCurrency());
        deliveryTerms.setTaxPercentage(deliveryTermsDTO.getTaxPercentage());
        deliveryTerms.setAdvancePaymentPercentage(deliveryTermsDTO.getAdvancePaymentPercentage());
        deliveryTerms.setDeliveryDate(deliveryTermsDTO.getDeliveryDate());
        deliveryTerms.setInvoiceAddress(deliveryTermsDTO.getInvoiceAddress());
        deliveryTerms.setDeliveryAddress(deliveryTermsDTO.getDeliveryAddress());
        deliveryTerms.setSoldToAddress(deliveryTermsDTO.getSoldToAddress());
        deliveryTerms.setDeliveryTerms(deliveryTermsDTO.getDeliveryTerms());
        
        return deliveryTermsRepository.save(deliveryTerms);
    }
    
    /**
     * Save delivery terms for vendor PO using VendorSplitPODTO data
     */
    public VendorPODeliveryTermsEntity saveDeliveryTermsForVendorPO(VendorSplitPODTO vendorPO, 
                                                                   PurchaseOrderDeliveryTermsDTO deliveryTermsDTO) {
        return saveDeliveryTerms(
            vendorPO.getVendorPoId(),
            vendorPO.getOriginalPrId(),
            vendorPO.getVendorId(),
            deliveryTermsDTO
        );
    }
    
    /**
     * Get delivery terms for a vendor PO
     */
    public Optional<VendorPODeliveryTermsEntity> getDeliveryTermsByVendorPoId(String vendorPoId) {
        return deliveryTermsRepository.findByVendorPoId(vendorPoId);
    }
    
    /**
     * Check if delivery terms exist for a vendor PO
     */
    public boolean deliveryTermsExist(String vendorPoId) {
        return deliveryTermsRepository.existsByVendorPoId(vendorPoId);
    }
}
