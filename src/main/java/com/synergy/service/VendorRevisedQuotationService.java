package com.synergy.service;

import com.synergy.dto.*;
import com.synergy.entity.*;
import com.synergy.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VendorRevisedQuotationService {
    
    private static final Logger logger = LoggerFactory.getLogger(VendorRevisedQuotationService.class);
    
    @Autowired
    private VendorRevisedQuotationRepository vendorRevisedQuotationRepository;
    
    @Autowired
    private VendorRevisedQuotationItemRepository vendorRevisedQuotationItemRepository;
    
    @Autowired
    private VendorRevisedQuotationChargeRepository vendorRevisedQuotationChargeRepository;
    
    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;
    
    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;
    
    @Autowired
    private VendorRepository vendorRepository;
    
    @Autowired
    private VendorBiddingTokenService tokenService;
    
    /**
     * Submit a revised quotation (vendor edit form submission)
     */
    @Transactional
    public VendorRevisedQuotationDTO submitRevisedQuotation(VendorRevisedQuotationDTO dto, String token) {
        logger.info("Submitting revised quotation for PR {} from vendor {} with token {}",
                dto.getPurchaseRequestId(), dto.getVendorId(), token != null ? "provided" : "not provided");
        
        // Validate token if provided
        VendorBiddingTokenEntity tokenEntity = null;
        if (token != null && !token.trim().isEmpty()) {
            Optional<VendorBiddingTokenEntity> tokenOpt = tokenService.validateToken(token);
            if (tokenOpt.isEmpty()) {
                throw new SecurityException(
                        "Invalid or expired bidding token. This link may have already been used or is no longer valid.");
            }
            tokenEntity = tokenOpt.get();
            
            // Verify that the token matches the PR and vendor in the DTO
            if (!tokenEntity.getPurchaseRequest().getId().equals(dto.getPurchaseRequestId()) ||
                    !tokenEntity.getVendor().getSrNo().equals(dto.getVendorId())) {
                throw new SecurityException("Token does not match the purchase request or vendor information.");
            }
            logger.info("Token validation successful for revised quotation PR {} and vendor {}",
                    dto.getPurchaseRequestId(), dto.getVendorId());
        }
        
        // Validate purchase request and vendor
        PurchaseRequestEntity pr = purchaseRequestRepository.findById(dto.getPurchaseRequestId())
                .orElseThrow(() -> new IllegalArgumentException(
                        "Purchase Request not found with ID: " + dto.getPurchaseRequestId()));
        
        VendorEntity vendor = vendorRepository.findById(dto.getVendorId())
                .orElseThrow(() -> new IllegalArgumentException("Vendor not found with srNo: " + dto.getVendorId()));
        
        // Validate that PR is approved (revised quotations are only for approved PRs)
        if (!"APPROVED".equals(pr.getStatus())) {
            throw new IllegalArgumentException("Revised quotations can only be submitted for approved PRs. Current status: " + pr.getStatus());
        }
        
        // Get the original quotation
        List<VendorQuotationEntity> originalQuotations = vendorQuotationRepository
                .findByPurchaseRequestIdAndVendorSrNo(dto.getPurchaseRequestId(), dto.getVendorId());
        
        if (originalQuotations.isEmpty()) {
            throw new IllegalArgumentException("No original quotation found for this PR and vendor");
        }
        
        VendorQuotationEntity originalQuotation = originalQuotations.get(0);
        
        // Check if revised quotation already exists
        Optional<VendorRevisedQuotationEntity> existingRevised = vendorRevisedQuotationRepository
                .findByOriginalQuotationId(originalQuotation.getId());
        
        VendorRevisedQuotationEntity revisedQuotation;
        boolean isUpdate = existingRevised.isPresent();
        
        if (isUpdate) {
            // Update existing revised quotation
            revisedQuotation = existingRevised.get();
            logger.info("Updating existing revised quotation {} for PR {} and vendor {}",
                    revisedQuotation.getId(), dto.getPurchaseRequestId(), dto.getVendorId());
        } else {
            // Create new revised quotation
            revisedQuotation = new VendorRevisedQuotationEntity();
            revisedQuotation.setPurchaseRequest(pr);
            revisedQuotation.setVendor(vendor);
            revisedQuotation.setOriginalQuotation(originalQuotation);
            logger.info("Creating new revised quotation for PR {} and vendor {}",
                    dto.getPurchaseRequestId(), dto.getVendorId());
        }
        
        // Set basic fields
        revisedQuotation.setSubmissionDate(new Date());
        revisedQuotation.setStatus("SUBMITTED");
        revisedQuotation.setCurrency(dto.getCurrency());
        revisedQuotation.setStockPoint(dto.getStockPoint());
        revisedQuotation.setReferenceNumber(dto.getReferenceNumber());
        
        // Set original values from the original quotation
        revisedQuotation.setOriginalTotalCost(originalQuotation.getTotalCost());
        revisedQuotation.setOriginalAdditionalChargesTotal(
                originalQuotation.getAdditionalCharges() != null ?
                originalQuotation.getAdditionalCharges().stream()
                        .mapToDouble(charge -> charge.getAmount() != null ? charge.getAmount() : 0.0)
                        .sum() : 0.0);
        revisedQuotation.setOriginalGrandTotal(originalQuotation.getGrandTotal());
        
        // Set revised values
        revisedQuotation.setRevisedTotalCost(dto.getRevisedTotalCost());
        revisedQuotation.setRevisedAdditionalChargesTotal(dto.getRevisedAdditionalChargesTotal());
        revisedQuotation.setRevisedGrandTotal(dto.getRevisedGrandTotal());
        
        // Calculate discount
        Double discountAmount = revisedQuotation.getOriginalGrandTotal() - revisedQuotation.getRevisedGrandTotal();
        revisedQuotation.setDiscountAmount(discountAmount);
        if (revisedQuotation.getOriginalGrandTotal() != null && revisedQuotation.getOriginalGrandTotal() > 0) {
            Double discountPercentage = (discountAmount / revisedQuotation.getOriginalGrandTotal()) * 100;
            revisedQuotation.setDiscountPercentage(discountPercentage);
        }
        
        // Save the revised quotation first
        revisedQuotation = vendorRevisedQuotationRepository.save(revisedQuotation);
        
        // Process items
        if (dto.getItems() != null && !dto.getItems().isEmpty()) {
            processRevisedQuotationItems(revisedQuotation, dto.getItems(), originalQuotation.getItems(), isUpdate);
        }
        
        // Process additional charges
        if (dto.getAdditionalCharges() != null && !dto.getAdditionalCharges().isEmpty()) {
            processRevisedQuotationCharges(revisedQuotation, dto.getAdditionalCharges(), 
                    originalQuotation.getAdditionalCharges(), isUpdate);
        }
        
        // Mark token as used if provided
        if (tokenEntity != null) {
            tokenService.markTokenAsUsed(token, revisedQuotation.getId());
        }
        
        logger.info("Successfully {} revised quotation {} for PR {} and vendor {}",
                isUpdate ? "updated" : "created", revisedQuotation.getId(), 
                dto.getPurchaseRequestId(), dto.getVendorId());
        
        return convertToDTO(revisedQuotation);
    }
    
    /**
     * Get revised quotations for a purchase request (for PDF generation dashboard)
     */
    public List<VendorRevisedQuotationDTO> getRevisedQuotationsForPR(Long prId) {
        logger.info("Getting revised quotations for PR {}", prId);
        
        List<VendorRevisedQuotationEntity> entities = vendorRevisedQuotationRepository
                .findByPurchaseRequestIdWithItemsAndCharges(prId);
        
        List<VendorRevisedQuotationDTO> result = entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        logger.info("Found {} revised quotations for PR {}", result.size(), prId);
        return result;
    }
    
    /**
     * Get revised quotation by ID (for PDF generation)
     */
    public VendorRevisedQuotationDTO getRevisedQuotationById(Long id) {
        VendorRevisedQuotationEntity entity = vendorRevisedQuotationRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Revised quotation not found with ID: " + id));

        return convertToDTO(entity);
    }

    /**
     * Get revised quotation by vendor PO ID
     * Parses vendor PO ID (e.g., "890-1") to extract PR ID and vendor sequence
     */
    public VendorRevisedQuotationDTO getRevisedQuotationByVendorPoId(String vendorPoId) {
        try {
            logger.info("Getting revised quotation for vendor PO ID: {}", vendorPoId);

            // Parse vendor PO ID - handle both formats: single vendor (890) and multi-vendor (890-1, 890-2)
            String[] parts = vendorPoId.split("-");
            Long prId;

            try {
                if (parts.length == 1) {
                    // Single vendor format: just PR ID (e.g., "890")
                    prId = Long.parseLong(parts[0]);
                    logger.info("Parsed single vendor PO - PR ID: {}", prId);
                } else if (parts.length == 2) {
                    // Multi-vendor format: PR ID with sequence (e.g., "890-1")
                    prId = Long.parseLong(parts[0]);
                    int vendorSequence = Integer.parseInt(parts[1]);
                    logger.info("Parsed multi-vendor PO - PR ID: {}, Vendor Sequence: {}", prId, vendorSequence);
                } else {
                    throw new IllegalArgumentException("Invalid vendor PO ID format. Expected format: 'prId' or 'prId-sequence'. Got: " + vendorPoId);
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid vendor PO ID format. Expected format: 'prId' or 'prId-sequence'. Got: " + vendorPoId);
            }

            // Get all revised quotations for this PR
            List<VendorRevisedQuotationEntity> revisedQuotations = vendorRevisedQuotationRepository.findByPurchaseRequestId(prId);

            if (revisedQuotations.isEmpty()) {
                logger.info("No revised quotations found for PR {}", prId);
                return null;
            }

            // Get purchase request to validate it exists
            PurchaseRequestEntity pr = purchaseRequestRepository.findById(prId)
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found with ID: " + prId));

            // Handle single vendor vs multiple vendor cases
            if (parts.length == 1) {
                // Single vendor case - should have only one revised quotation
                if (revisedQuotations.size() == 1) {
                    logger.info("Found single revised quotation for single vendor PR {}", prId);
                    return convertToDTO(revisedQuotations.get(0));
                } else if (revisedQuotations.isEmpty()) {
                    logger.info("No revised quotations found for single vendor PR {}", prId);
                    return null;
                } else {
                    logger.warn("Multiple revised quotations found for single vendor PR {}, returning first one", prId);
                    return convertToDTO(revisedQuotations.get(0));
                }
            } else {
                // Multiple vendor case - use sequence to find the right quotation
                try {
                    int sequence = Integer.parseInt(parts[1]);
                    if (sequence > 0 && sequence <= revisedQuotations.size()) {
                        VendorRevisedQuotationEntity selectedQuotation = revisedQuotations.get(sequence - 1);
                        logger.info("Found revised quotation for vendor PO {} (sequence {})", vendorPoId, sequence);
                        return convertToDTO(selectedQuotation);
                    } else {
                        logger.warn("Invalid sequence {} for vendor PO {}, available quotations: {}", sequence, vendorPoId, revisedQuotations.size());
                        return null;
                    }
                } catch (NumberFormatException e) {
                    logger.error("Invalid sequence number in vendor PO ID: {}", parts[1]);
                    throw new IllegalArgumentException("Invalid sequence number in vendor PO ID: " + parts[1]);
                }
            }

        } catch (Exception e) {
            logger.error("Error getting revised quotation for vendor PO ID {}: {}", vendorPoId, e.getMessage(), e);
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new RuntimeException("Failed to get revised quotation for vendor PO " + vendorPoId, e);
        }
    }

    /**
     * Process revised quotation items
     */
    private void processRevisedQuotationItems(VendorRevisedQuotationEntity revisedQuotation,
                                            List<VendorRevisedQuotationItemDTO> itemDTOs,
                                            List<VendorQuotationItemEntity> originalItems,
                                            boolean isUpdate) {

        // Create a map of original items by unique code for easy lookup
        Map<String, VendorQuotationItemEntity> originalItemsMap = originalItems.stream()
                .collect(Collectors.toMap(VendorQuotationItemEntity::getUniqueCode, item -> item));

        if (isUpdate) {
            // Clear existing items for update
            vendorRevisedQuotationItemRepository.deleteAll(revisedQuotation.getItems());
        }

        List<VendorRevisedQuotationItemEntity> newItems = new ArrayList<>();

        for (VendorRevisedQuotationItemDTO itemDTO : itemDTOs) {
            VendorRevisedQuotationItemEntity item = new VendorRevisedQuotationItemEntity();
            item.setRevisedQuotation(revisedQuotation);
            item.setUniqueCode(itemDTO.getUniqueCode());
            item.setProductName(itemDTO.getProductName());
            item.setQuantity(itemDTO.getQuantity());
            item.setUnitOfMeasure(itemDTO.getUnitOfMeasure());
            item.setDeliveryDate(itemDTO.getDeliveryDate());
            item.setVendorRemarks(itemDTO.getVendorRemarks());
            item.setMaterialFamily(itemDTO.getMaterialFamily());

            // Set original values from original quotation
            VendorQuotationItemEntity originalItem = originalItemsMap.get(itemDTO.getUniqueCode());
            if (originalItem != null) {
                item.setOriginalUnitPrice(originalItem.getUnitPrice());
                item.setOriginalTotalCost(originalItem.getTotalCost());
            }

            // Set revised values
            item.setRevisedUnitPrice(itemDTO.getRevisedUnitPrice());
            item.setRevisedTotalCost(itemDTO.getRevisedTotalCost());

            // Calculate discount for this item
            if (item.getOriginalTotalCost() != null && item.getRevisedTotalCost() != null) {
                Double itemDiscountAmount = item.getOriginalTotalCost() - item.getRevisedTotalCost();
                item.setDiscountAmount(itemDiscountAmount);
                if (item.getOriginalTotalCost() > 0) {
                    Double itemDiscountPercentage = (itemDiscountAmount / item.getOriginalTotalCost()) * 100;
                    item.setDiscountPercentage(itemDiscountPercentage);
                }
            }

            newItems.add(item);
        }

        vendorRevisedQuotationItemRepository.saveAll(newItems);
        revisedQuotation.setItems(newItems);
    }

    /**
     * Process revised quotation additional charges
     */
    private void processRevisedQuotationCharges(VendorRevisedQuotationEntity revisedQuotation,
                                              List<VendorRevisedQuotationChargeDTO> chargeDTOs,
                                              List<VendorQuotationChargeEntity> originalCharges,
                                              boolean isUpdate) {

        // Create a map of original charges by charge name for easy lookup
        Map<String, VendorQuotationChargeEntity> originalChargesMap = originalCharges.stream()
                .collect(Collectors.toMap(VendorQuotationChargeEntity::getDescription, charge -> charge));

        if (isUpdate) {
            // Clear existing charges for update
            vendorRevisedQuotationChargeRepository.deleteAll(revisedQuotation.getAdditionalCharges());
        }

        List<VendorRevisedQuotationChargeEntity> newCharges = new ArrayList<>();

        for (VendorRevisedQuotationChargeDTO chargeDTO : chargeDTOs) {
            VendorRevisedQuotationChargeEntity charge = new VendorRevisedQuotationChargeEntity();
            charge.setRevisedQuotation(revisedQuotation);
            charge.setChargeName(chargeDTO.getChargeName());

            // Set original values from original quotation
            VendorQuotationChargeEntity originalCharge = originalChargesMap.get(chargeDTO.getChargeName());
            if (originalCharge != null) {
                charge.setOriginalChargeAmount(originalCharge.getAmount());
            }

            // Set revised values
            charge.setRevisedChargeAmount(chargeDTO.getRevisedChargeAmount());

            // Calculate discount for this charge
            if (charge.getOriginalChargeAmount() != null && charge.getRevisedChargeAmount() != null) {
                Double chargeDiscountAmount = charge.getOriginalChargeAmount() - charge.getRevisedChargeAmount();
                charge.setDiscountAmount(chargeDiscountAmount);
                if (charge.getOriginalChargeAmount() > 0) {
                    Double chargeDiscountPercentage = (chargeDiscountAmount / charge.getOriginalChargeAmount()) * 100;
                    charge.setDiscountPercentage(chargeDiscountPercentage);
                }
            }

            newCharges.add(charge);
        }

        vendorRevisedQuotationChargeRepository.saveAll(newCharges);
        revisedQuotation.setAdditionalCharges(newCharges);
    }

    /**
     * Convert entity to DTO
     */
    private VendorRevisedQuotationDTO convertToDTO(VendorRevisedQuotationEntity entity) {
        VendorRevisedQuotationDTO dto = new VendorRevisedQuotationDTO();

        // Basic fields
        dto.setId(entity.getId());
        dto.setPurchaseRequestId(entity.getPurchaseRequest().getId());
        dto.setVendorId(entity.getVendor().getSrNo());
        dto.setVendorCompanyName(entity.getVendor().getCompanyName());
        dto.setSubmissionDate(entity.getSubmissionDate());
        dto.setStatus(entity.getStatus());
        dto.setCurrency(entity.getCurrency());
        dto.setStockPoint(entity.getStockPoint());
        dto.setReferenceNumber(entity.getReferenceNumber());

        // Original values
        dto.setOriginalTotalCost(entity.getOriginalTotalCost());
        dto.setOriginalAdditionalChargesTotal(entity.getOriginalAdditionalChargesTotal());
        dto.setOriginalGrandTotal(entity.getOriginalGrandTotal());

        // Revised values
        dto.setRevisedTotalCost(entity.getRevisedTotalCost());
        dto.setRevisedAdditionalChargesTotal(entity.getRevisedAdditionalChargesTotal());
        dto.setRevisedGrandTotal(entity.getRevisedGrandTotal());

        // Discount information
        dto.setDiscountAmount(entity.getDiscountAmount());
        dto.setDiscountPercentage(entity.getDiscountPercentage());

        // Convert items
        if (entity.getItems() != null) {
            List<VendorRevisedQuotationItemDTO> itemDTOs = entity.getItems().stream()
                    .map(this::convertItemToDTO)
                    .collect(Collectors.toList());
            dto.setItems(itemDTOs);
        }

        // Convert additional charges
        if (entity.getAdditionalCharges() != null) {
            List<VendorRevisedQuotationChargeDTO> chargeDTOs = entity.getAdditionalCharges().stream()
                    .map(this::convertChargeToDTO)
                    .collect(Collectors.toList());
            dto.setAdditionalCharges(chargeDTOs);
        }

        return dto;
    }

    /**
     * Convert item entity to DTO
     */
    private VendorRevisedQuotationItemDTO convertItemToDTO(VendorRevisedQuotationItemEntity entity) {
        VendorRevisedQuotationItemDTO dto = new VendorRevisedQuotationItemDTO();

        dto.setId(entity.getId());
        dto.setUniqueCode(entity.getUniqueCode());
        dto.setProductName(entity.getProductName());
        dto.setQuantity(entity.getQuantity());
        dto.setUnitOfMeasure(entity.getUnitOfMeasure());
        dto.setDeliveryDate(entity.getDeliveryDate());
        dto.setVendorRemarks(entity.getVendorRemarks());
        dto.setMaterialFamily(entity.getMaterialFamily());

        // Original values
        dto.setOriginalUnitPrice(entity.getOriginalUnitPrice());
        dto.setOriginalTotalCost(entity.getOriginalTotalCost());

        // Revised values
        dto.setRevisedUnitPrice(entity.getRevisedUnitPrice());
        dto.setRevisedTotalCost(entity.getRevisedTotalCost());

        // Discount information
        dto.setDiscountAmount(entity.getDiscountAmount());
        dto.setDiscountPercentage(entity.getDiscountPercentage());

        return dto;
    }

    /**
     * Convert charge entity to DTO
     */
    private VendorRevisedQuotationChargeDTO convertChargeToDTO(VendorRevisedQuotationChargeEntity entity) {
        VendorRevisedQuotationChargeDTO dto = new VendorRevisedQuotationChargeDTO();

        dto.setId(entity.getId());
        dto.setChargeName(entity.getChargeName());

        // Original values
        dto.setOriginalChargeAmount(entity.getOriginalChargeAmount());

        // Revised values
        dto.setRevisedChargeAmount(entity.getRevisedChargeAmount());

        // Discount information
        dto.setDiscountAmount(entity.getDiscountAmount());
        dto.setDiscountPercentage(entity.getDiscountPercentage());

        return dto;
    }
}
