package com.synergy.service;

import com.synergy.dto.AddressDTO;
import com.synergy.dto.ContractorDTO;
import com.synergy.entity.ContractorEntity;
import com.synergy.repository.ContractorRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ContractorService {

    @Autowired
    private ContractorRepository contractorRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public List<ContractorDTO> getAllContractors() {
        return contractorRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public ContractorDTO getContractorById(Long id) {
        Optional<ContractorEntity> contractorOptional = contractorRepository.findById(id);
        if (contractorOptional.isPresent()) {
            return convertToDTO(contractorOptional.get());
        } else {
            throw new IllegalArgumentException("Contractor not found with ID: " + id);
        }
    }

    @Transactional
    public ContractorDTO createContractor(ContractorDTO contractorDTO) throws JsonProcessingException {
        ContractorEntity entity = convertToEntity(contractorDTO);
        ContractorEntity savedEntity = contractorRepository.save(entity);
        return convertToDTO(savedEntity);
    }

    @Transactional
    public ContractorDTO updateContractor(Long id, ContractorDTO contractorDTO) throws JsonProcessingException {
        Optional<ContractorEntity> contractorOptional = contractorRepository.findById(id);
        if (contractorOptional.isPresent()) {
            // Update the entity with DTO values
            ContractorEntity updatedEntity = convertToEntity(contractorDTO);
            updatedEntity.setSrNo(id); // Ensure ID is preserved

            ContractorEntity savedEntity = contractorRepository.save(updatedEntity);
            return convertToDTO(savedEntity);
        } else {
            throw new IllegalArgumentException("Contractor not found with ID: " + id);
        }
    }

    @Transactional
    public void deleteContractor(Long id) {
        if (contractorRepository.existsById(id)) {
            contractorRepository.deleteById(id);
        } else {
            throw new IllegalArgumentException("Contractor not found with ID: " + id);
        }
    }

    private ContractorDTO convertToDTO(ContractorEntity entity) {
        ContractorDTO dto = new ContractorDTO();
        dto.setSrNo(entity.getSrNo());
        dto.setCompanyName(entity.getVendorName()); // Map contractor_name to companyName
        dto.setGstNumber(entity.getGstNumber());
        dto.setPanNo(entity.getPanNo());
        dto.setVendorName(entity.getVendorName());
        dto.setWhatsappNumber(entity.getWhatsappNumber());
        dto.setContactNumber(entity.getContactNumber());
        dto.setEmailId(entity.getEmailId());

        // Convert JSON string to List<AddressDTO> for addresses
        try {
            if (entity.getAddress() != null && !entity.getAddress().isEmpty()) {
                List<AddressDTO> addresses = objectMapper.readValue(entity.getAddress(), new TypeReference<List<AddressDTO>>() {});
                dto.setAddresses(addresses);
            }
        } catch (JsonProcessingException e) {
            // If there's an error parsing the JSON, set an empty list
            dto.setAddresses(List.of());
        }

        dto.setItems(entity.getItems());
        dto.setVendorCode(entity.getVendorCode());
        dto.setCreditDays(entity.getCreditDays());
        dto.setCreditLimit(entity.getCreditLimit());
        dto.setRemark(entity.getRemark());
        dto.setContractorCountryType(entity.getContractorCountryType());
        dto.setCreditTerms(entity.getCreditTerms());
        dto.setPaymentTerms(entity.getPaymentTerms());
        return dto;
    }

    private ContractorEntity convertToEntity(ContractorDTO dto) throws JsonProcessingException {
        ContractorEntity entity = new ContractorEntity();
        entity.setSrNo(dto.getSrNo());
        entity.setVendorName(dto.getCompanyName()); // Map companyName to contractor_name
        entity.setGstNumber(dto.getGstNumber());
        entity.setPanNo(dto.getPanNo());
        entity.setWhatsappNumber(dto.getWhatsappNumber());
        entity.setContactNumber(dto.getContactNumber());
        entity.setEmailId(dto.getEmailId());

        // Convert List<AddressDTO> to JSON string for addresses
        if (dto.getAddresses() != null && !dto.getAddresses().isEmpty()) {
            String addressesJson = objectMapper.writeValueAsString(dto.getAddresses());
            entity.setAddress(addressesJson);

            // Set the first address's fields to the entity fields for filtering
            AddressDTO firstAddress = dto.getAddresses().get(0);
            entity.setCountry(firstAddress.getCountry());
            entity.setCity(firstAddress.getCity());
            entity.setState(firstAddress.getState());
            entity.setVendorType(firstAddress.getType());
        }

        entity.setItems(dto.getItems());
        entity.setVendorCode(dto.getVendorCode());
        entity.setCreditDays(dto.getCreditDays());
        entity.setCreditLimit(dto.getCreditLimit());
        entity.setRemark(dto.getRemark());
        entity.setContractorCountryType(dto.getContractorCountryType());
        entity.setCreditTerms(dto.getCreditTerms());
        entity.setPaymentTerms(dto.getPaymentTerms());
        return entity;
    }

    public List<String> getAllContractorNames() {
        return contractorRepository.findAll().stream()
                .map(ContractorEntity::getVendorName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .collect(Collectors.toList());
    }

}
