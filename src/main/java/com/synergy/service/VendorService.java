package com.synergy.service;

import com.synergy.dto.AddressDTO;
import com.synergy.dto.VendorDTO;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.entity.VendorBiddingTokenEntity;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.VendorRepository;
import com.synergy.repository.VendorBiddingTokenRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class VendorService {

	private static final Logger logger = LoggerFactory.getLogger(VendorService.class);

	@Autowired
	private VendorRepository vendorRepository;

	@Autowired
	private PurchaseRequestRepository purchaseRequestRepository;

	@Autowired
	private VendorBiddingTokenRepository vendorBiddingTokenRepository;

	private final ObjectMapper objectMapper = new ObjectMapper();

	public List<VendorDTO> getAllVendors() {
		return vendorRepository.findAll().stream().map(this::convertToDTO).collect(Collectors.toList());
	}

	public VendorDTO getVendorById(Long id) {
		Optional<VendorEntity> vendorOptional = vendorRepository.findById(id);
		if (vendorOptional.isPresent()) {
			return convertToDTO(vendorOptional.get());
		} else {
			throw new IllegalArgumentException("Vendor not found with ID: " + id);
		}
	}

	@Transactional
	public VendorDTO createVendor(VendorDTO vendorDTO) throws JsonProcessingException {
		VendorEntity entity = convertToEntity(vendorDTO);
		VendorEntity savedEntity = vendorRepository.save(entity);
		return convertToDTO(savedEntity);
	}

	@Transactional
	public VendorDTO updateVendor(Long id, VendorDTO vendorDTO) throws JsonProcessingException {
		Optional<VendorEntity> vendorOptional = vendorRepository.findById(id);
		if (vendorOptional.isPresent()) {
			// Update the entity with DTO values
			VendorEntity updatedEntity = convertToEntity(vendorDTO);
			updatedEntity.setSrNo(id); // Ensure ID is preserved

			VendorEntity savedEntity = vendorRepository.save(updatedEntity);
			return convertToDTO(savedEntity);
		} else {
			throw new IllegalArgumentException("Vendor not found with ID: " + id);
		}
	}

	@Transactional
	public void deleteVendor(Long srNo) {
		VendorEntity vendor = vendorRepository.findById(srNo)
				.orElseThrow(() -> new IllegalArgumentException("Vendor not found"));

		vendor.setStatus("INACTIVE");
		vendorRepository.save(vendor);
	}

	private VendorDTO convertToDTO(VendorEntity entity) {
		VendorDTO dto = new VendorDTO();
		dto.setSrNo(entity.getSrNo());
		dto.setCompanyName(entity.getCompanyName());
		dto.setGstNumber(entity.getGstNumber());
		dto.setPanNo(entity.getPanNo());
		dto.setVendorName(entity.getVendorName());
		dto.setWhatsappNumber(entity.getWhatsappNumber());
		dto.setContactNumber(entity.getContactNumber());
		dto.setEmailId(entity.getEmailId());

		// Convert JSON string to List<AddressDTO> for addresses
		try {
			if (entity.getAddress() != null && !entity.getAddress().isEmpty()) {
				List<AddressDTO> addresses = objectMapper.readValue(entity.getAddress(),
						new TypeReference<List<AddressDTO>>() {
						});
				dto.setAddresses(addresses);
			}
		} catch (JsonProcessingException e) {
			// If there's an error parsing the JSON, set an empty list
			dto.setAddresses(List.of());
		}

		// Add country, city, state fields
		dto.setCountry(entity.getCountry());
		dto.setCity(entity.getCity());
		dto.setState(entity.getState());

		dto.setVendorCode(entity.getVendorCode());
		dto.setCreditDays(entity.getCreditDays());
		dto.setCreditLimit(entity.getCreditLimit());
		dto.setRemark(entity.getRemark());
		dto.setVendorType(entity.getVendorType());
		dto.setStatus(entity.getStatus());
		return dto;
	}

	private VendorEntity convertToEntity(VendorDTO dto) throws JsonProcessingException {
		VendorEntity entity = new VendorEntity();
		entity.setSrNo(dto.getSrNo());
		entity.setCompanyName(dto.getCompanyName());
		entity.setGstNumber(dto.getGstNumber());
		entity.setPanNo(dto.getPanNo());
		entity.setVendorName(dto.getVendorName());
		entity.setWhatsappNumber(dto.getWhatsappNumber());
		entity.setContactNumber(dto.getContactNumber());
		entity.setEmailId(dto.getEmailId());

		// Convert List<AddressDTO> to JSON string for addresses
		if (dto.getAddresses() != null && !dto.getAddresses().isEmpty()) {
			String addressesJson = objectMapper.writeValueAsString(dto.getAddresses());
			entity.setAddress(addressesJson);

			// Set the first address's fields to the entity fields for filtering
			AddressDTO firstAddress = dto.getAddresses().get(0);
			entity.setCountry(firstAddress.getCountry());
			entity.setCity(firstAddress.getCity());
			entity.setState(firstAddress.getState());

			// Only set vendor type from address if it's not already set in the DTO
			if (dto.getVendorType() == null && firstAddress.getType() != null) {
				entity.setVendorType(firstAddress.getType());
			}
		} else {
			// If no addresses, use the direct fields if provided
			entity.setCountry(dto.getCountry());
			entity.setCity(dto.getCity());
			entity.setState(dto.getState());
		}

		// Set vendor type from DTO if provided
		if (dto.getVendorType() != null) {
			entity.setVendorType(dto.getVendorType());
		}

		entity.setVendorCode(dto.getVendorCode());
		entity.setCreditDays(dto.getCreditDays());
		entity.setCreditLimit(dto.getCreditLimit());
		entity.setRemark(dto.getRemark());
		entity.setStatus(dto.getStatus() != null ? dto.getStatus() : "ACTIVE");
		return entity;
	}

	public Map<String, Object> bulkUploadVendors(MultipartFile file) throws IOException {
		Map<String, Object> result = new HashMap<>();
		List<String> errors = new ArrayList<>();
		List<VendorEntity> successfulVendors = new ArrayList<>();
		int totalRows = 0;
		int successCount = 0;
		int errorCount = 0;

		try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
			Sheet sheet = workbook.getSheetAt(0);

			// Skip header row (row 0)
			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);
				if (row == null) continue;

				totalRows++;

				try {
					VendorEntity vendor = parseRowToVendor(row, i + 1);
					if (vendor != null) {
						VendorEntity savedVendor = vendorRepository.save(vendor);
						successfulVendors.add(savedVendor);
						successCount++;
						logger.info("Successfully saved vendor: {} at row {}", vendor.getCompanyName(), i + 1);
					}
				} catch (Exception e) {
					errorCount++;
					String errorMsg = "Row " + (i + 1) + ": " + e.getMessage();
					errors.add(errorMsg);
					logger.error("Error processing row {}: {}", i + 1, e.getMessage());
				}
			}
		} catch (Exception e) {
			logger.error("Error reading Excel file: {}", e.getMessage());
			throw new IOException("Error reading Excel file: " + e.getMessage());
		}

		result.put("totalRows", totalRows);
		result.put("successCount", successCount);
		result.put("errorCount", errorCount);
		result.put("errors", errors);
		result.put("successfulVendors", successfulVendors.size());

		logger.info("Bulk upload completed. Total: {}, Success: {}, Errors: {}", totalRows, successCount, errorCount);
		return result;
	}

	private VendorEntity parseRowToVendor(Row row, int rowNumber) {
		VendorEntity vendor = new VendorEntity();

		try {
			// Column mapping based on vendor fields
			vendor.setCompanyName(getCellValueAsString(row.getCell(0))); // A: Company Name
			vendor.setVendorName(getCellValueAsString(row.getCell(1))); // B: Vendor Name
			vendor.setGstNumber(getCellValueAsString(row.getCell(2))); // C: GST Number
			vendor.setPanNo(getCellValueAsString(row.getCell(3))); // D: PAN Number
			vendor.setContactNumber(getCellValueAsString(row.getCell(4))); // E: Contact Number
			vendor.setWhatsappNumber(getCellValueAsString(row.getCell(5))); // F: WhatsApp Number
			vendor.setEmailId(getCellValueAsString(row.getCell(6))); // G: Email ID

			// Handle address - convert to JSON format
			String addressText = getCellValueAsString(row.getCell(7)); // H: Address
			String cityText = getCellValueAsString(row.getCell(8)); // I: City
			String stateText = getCellValueAsString(row.getCell(9)); // J: State
			String countryText = getCellValueAsString(row.getCell(10)); // K: Country
			String categoryText = getCellValueAsString(row.getCell(15)); // P: Category (Production Stage)
			String subCategoryText = getCellValueAsString(row.getCell(16)); // Q: Sub Category (Material Family)

			try {
				// Create address object and convert to JSON
				List<AddressDTO> addresses = new ArrayList<>();
				AddressDTO address = new AddressDTO();
				address.setType(categoryText != null ? categoryText.trim() : "Primary");
				address.setItems(subCategoryText != null ? subCategoryText.trim() : "");
				address.setAddress(addressText != null ? addressText.trim() : "");
				address.setCity(cityText != null ? cityText.trim() : "");
				address.setState(stateText != null ? stateText.trim() : "");
				address.setCountry(countryText != null ? countryText.trim() : "");
				addresses.add(address);

				// Convert to JSON string for storage
				String addressJson = objectMapper.writeValueAsString(addresses);
				vendor.setAddress(addressJson);
			} catch (JsonProcessingException e) {
				logger.warn("Error converting address to JSON for row {}: {}", rowNumber, e.getMessage());
				vendor.setAddress("[]"); // Set empty JSON array as fallback
			}

			vendor.setCity(cityText != null ? cityText.trim() : ""); // I: City
			vendor.setState(stateText != null ? stateText.trim() : ""); // J: State
			vendor.setCountry(countryText != null ? countryText.trim() : ""); // K: Country
			vendor.setVendorType(getCellValueAsString(row.getCell(11))); // L: Vendor Type (moved up)
			vendor.setCreditDays(getCellValueAsString(row.getCell(12))); // M: Credit Days
			vendor.setCreditLimit(getCellValueAsBigDecimal(row.getCell(13))); // N: Credit Limit
			vendor.setRemark(getCellValueAsString(row.getCell(14))); // O: Remark

			// Validate required fields
			if (vendor.getCompanyName() == null || vendor.getCompanyName().trim().isEmpty()) {
				throw new IllegalArgumentException("Company Name is required");
			}

			// Auto-generate vendor code if not provided
			if (vendor.getVendorCode() == null || vendor.getVendorCode().trim().isEmpty()) {
				long count = vendorRepository.count();
				String generatedCode = "VEN" + String.format("%04d", count + 1);
				vendor.setVendorCode(generatedCode);
			}

			// Set default status
			vendor.setStatus("ACTIVE");

			return vendor;
		} catch (Exception e) {
			throw new RuntimeException("Error parsing row data: " + e.getMessage());
		}
	}

	private String getCellValueAsString(Cell cell) {
		if (cell == null) return null;

		switch (cell.getCellType()) {
			case STRING:
				return cell.getStringCellValue().trim();
			case NUMERIC:
				if (DateUtil.isCellDateFormatted(cell)) {
					return cell.getDateCellValue().toString();
				} else {
					return String.valueOf((long) cell.getNumericCellValue());
				}
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case FORMULA:
				return cell.getCellFormula();
			default:
				return null;
		}
	}

	private BigDecimal getCellValueAsBigDecimal(Cell cell) {
		if (cell == null) return null;

		switch (cell.getCellType()) {
			case NUMERIC:
				return BigDecimal.valueOf(cell.getNumericCellValue());
			case STRING:
				try {
					return new BigDecimal(cell.getStringCellValue().trim());
				} catch (NumberFormatException e) {
					return null;
				}
			default:
				return null;
		}
	}

	public void generateBulkUploadTemplate(jakarta.servlet.http.HttpServletResponse response) throws IOException {
		try (Workbook workbook = new XSSFWorkbook()) {
			Sheet sheet = workbook.createSheet("Vendors Template");

			// Create header row
			Row headerRow = sheet.createRow(0);
			String[] headers = {
				"Company Name*", "Vendor Name", "GST Number", "PAN Number", "Contact Number",
				"WhatsApp Number", "Email ID", "Address", "City", "State", "Country",
				"Vendor Type", "Credit Days", "Credit Limit", "Remark", "Category (Production Stage)",
				"Sub Category (Material Family)"
			};

			// Create header style
			CellStyle headerStyle = workbook.createCellStyle();
			Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerFont.setColor(IndexedColors.WHITE.getIndex());
			headerStyle.setFont(headerFont);
			headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			headerStyle.setBorderBottom(BorderStyle.THIN);
			headerStyle.setBorderTop(BorderStyle.THIN);
			headerStyle.setBorderRight(BorderStyle.THIN);
			headerStyle.setBorderLeft(BorderStyle.THIN);

			// Apply headers
			for (int i = 0; i < headers.length; i++) {
				Cell cell = headerRow.createCell(i);
				cell.setCellValue(headers[i]);
				cell.setCellStyle(headerStyle);
				sheet.autoSizeColumn(i);
			}

			// Add sample data rows
			Row sampleRow1 = sheet.createRow(1);
			sampleRow1.createCell(0).setCellValue("ABC Steel Industries");
			sampleRow1.createCell(1).setCellValue("Rajesh Kumar");
			sampleRow1.createCell(2).setCellValue("27**********1Z5");
			sampleRow1.createCell(3).setCellValue("**********");
			sampleRow1.createCell(4).setCellValue("9876543210");
			sampleRow1.createCell(5).setCellValue("9876543210");
			sampleRow1.createCell(6).setCellValue("<EMAIL>");
			sampleRow1.createCell(7).setCellValue("123 Industrial Area, Sector 5");
			sampleRow1.createCell(8).setCellValue("Mumbai");
			sampleRow1.createCell(9).setCellValue("Maharashtra");
			sampleRow1.createCell(10).setCellValue("India");
			sampleRow1.createCell(11).setCellValue("Supplier");
			sampleRow1.createCell(12).setCellValue("30");
			sampleRow1.createCell(13).setCellValue(500000.0);
			sampleRow1.createCell(14).setCellValue("Reliable steel supplier");
			sampleRow1.createCell(15).setCellValue("Plates");
			sampleRow1.createCell(16).setCellValue("Outfittings");

			Row sampleRow2 = sheet.createRow(2);
			sampleRow2.createCell(0).setCellValue("XYZ Marine Equipment");
			sampleRow2.createCell(1).setCellValue("Priya Sharma");
			sampleRow2.createCell(2).setCellValue("19**********2H3");
			sampleRow2.createCell(3).setCellValue("**********");
			sampleRow2.createCell(4).setCellValue("8765432109");
			sampleRow2.createCell(5).setCellValue("8765432109");
			sampleRow2.createCell(6).setCellValue("<EMAIL>");
			sampleRow2.createCell(7).setCellValue("456 Port Road, Marine Drive");
			sampleRow2.createCell(8).setCellValue("Chennai");
			sampleRow2.createCell(9).setCellValue("Tamil Nadu");
			sampleRow2.createCell(10).setCellValue("India");
			sampleRow2.createCell(11).setCellValue("Equipment Supplier");
			sampleRow2.createCell(12).setCellValue("45");
			sampleRow2.createCell(13).setCellValue(750000.0);
			sampleRow2.createCell(14).setCellValue("Marine equipment specialist");
			sampleRow2.createCell(15).setCellValue("Plates");
			sampleRow2.createCell(16).setCellValue("Outfittings");

			// Auto-size columns
			for (int i = 0; i < headers.length; i++) {
				sheet.autoSizeColumn(i);
			}

			// Set response headers
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment; filename=vendors_bulk_upload_template.xlsx");
			System.out.println(response.getHeader("Content-Disposition"));

			// Write to response
			workbook.write(response.getOutputStream());
			logger.info("Vendor bulk upload template generated successfully");

		} catch (Exception e) {
			logger.error("Error generating vendor bulk upload template: {}", e.getMessage());
			throw new IOException("Error generating template: " + e.getMessage());
		}
	}
}
