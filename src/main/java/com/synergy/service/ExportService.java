package com.synergy.service;

import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.*;
import com.itextpdf.text.pdf.draw.LineSeparator;
import com.synergy.dto.PurchaseOrderDeliveryTermsDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.entity.*;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.ShipbuildersItemRepository;
import com.synergy.repository.MaterialRequisitionRepository;
import com.synergy.repository.VendorQuotationRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.synergy.dto.PurchaseRequestLightDTO;

@Service
public class ExportService {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Autowired
    private MaterialRequisitionRepository materialRequisitionRepository;

    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;

    public void exportAllToPdf(List<MaterialRequisitionEntity> requisitions, HttpServletResponse response) throws DocumentException, IOException {
        com.itextpdf.text.Document document = new com.itextpdf.text.Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=material_requisitions.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        com.itextpdf.text.Font titleFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 18, com.itextpdf.text.Font.BOLD);
        Paragraph title = new Paragraph("Material Requisitions", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(new float[]{1, 2, 3, 2, 2, 1, 1});
        table.setWidthPercentage(100);

        // Add table headers
        com.itextpdf.text.Font headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 10, com.itextpdf.text.Font.BOLD);
        String[] headers = {"ID", "Date", "Contractor Name / Client Name", "Yard Number", "Project Name", "Line Items", "Action"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        com.itextpdf.text.Font dataFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 9, com.itextpdf.text.Font.NORMAL);
        for (MaterialRequisitionEntity requisition : requisitions) {
            table.addCell(new Phrase(requisition.getRequisitionId(), dataFont));
            table.addCell(new Phrase(dateFormat.format(requisition.getCreatedDate()), dataFont));
            table.addCell(new Phrase(requisition.getContractorName(), dataFont));
            table.addCell(new Phrase(requisition.getYardNumber(), dataFont));
            table.addCell(new Phrase(requisition.getProjectName(), dataFont));
            table.addCell(new Phrase(String.valueOf(requisition.getLineItems().size()), dataFont));
            table.addCell("");
        }

        document.add(table);
        document.close();
    }

    public void exportAllToExcel(List<MaterialRequisitionEntity> requisitions, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=material_requisitions.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Material Requisitions");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"ID", "Date", "Contractor Name / Client Name", "Yard Number", "Project Name", "Line Items"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (MaterialRequisitionEntity requisition : requisitions) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(requisition.getRequisitionId());

            Cell dateCell = row.createCell(1);
            dateCell.setCellValue(requisition.getCreatedDate());
            dateCell.setCellStyle(dateStyle);

            row.createCell(2).setCellValue(requisition.getContractorName());
            row.createCell(3).setCellValue(requisition.getYardNumber());
            row.createCell(4).setCellValue(requisition.getProjectName());
            row.createCell(5).setCellValue(requisition.getLineItems().size());
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Purchase Request export methods
    public void exportPurchaseRequestsToPdf(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response) throws DocumentException, IOException {
        com.itextpdf.text.Document document = new com.itextpdf.text.Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_requests.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Purchase Requests", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(new float[]{1, 2, 3, 2, 2, 1, 1});
        table.setWidthPercentage(100);

        // Add table headers
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (PurchaseRequestEntity pr : purchaseRequests) {
            table.addCell(new Phrase(pr.getPrId(), dataFont));
            table.addCell(new Phrase(dateFormat.format(pr.getCreatedDate()), dataFont));
            table.addCell(new Phrase(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName(), dataFont));
            table.addCell(new Phrase(pr.getYardNumber(), dataFont));
            table.addCell(new Phrase(pr.getProjectName(), dataFont));
            table.addCell(new Phrase(String.valueOf(pr.getLineItems().size()), dataFont));
            table.addCell(new Phrase(pr.getStatus(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportPurchaseRequestsToExcel(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_requests.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Purchase Requests");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (PurchaseRequestEntity pr : purchaseRequests) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(pr.getPrId());

            Cell dateCell = row.createCell(1);
            dateCell.setCellValue(pr.getCreatedDate());
            dateCell.setCellStyle(dateStyle);

            row.createCell(2).setCellValue(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName());
            row.createCell(3).setCellValue(pr.getYardNumber());
            row.createCell(4).setCellValue(pr.getProjectName());
            row.createCell(5).setCellValue(pr.getLineItems().size());
            row.createCell(6).setCellValue(pr.getStatus());
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Vendor Quotation export methods
    public void exportVendorQuotationsToPdf(List<VendorQuotationEntity> quotations, HttpServletResponse response) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=vendor_quotations.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Vendor Quotations", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(new float[]{1, 2, 3, 2, 2, 2});
        table.setWidthPercentage(100);

        // Add table headers
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"ID", "Vendor", "PR ID", "Submission Date", "Total Cost", "Status"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (VendorQuotationEntity quotation : quotations) {
            table.addCell(new Phrase(String.valueOf(quotation.getId()), dataFont));
            table.addCell(new Phrase(quotation.getVendor().getCompanyName(), dataFont));
            table.addCell(new Phrase(quotation.getPurchaseRequest().getPrId(), dataFont));
            table.addCell(new Phrase(dateFormat.format(quotation.getSubmissionDate()), dataFont));
            table.addCell(new Phrase(String.valueOf(quotation.getTotalCost()), dataFont));
            table.addCell(new Phrase(quotation.getStatus(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportVendorQuotationsToExcel(List<VendorQuotationEntity> quotations, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=vendor_quotations.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Vendor Quotations");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"ID", "Vendor", "PR ID", "Submission Date", "Total Cost", "Status"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (VendorQuotationEntity quotation : quotations) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(quotation.getId());
            row.createCell(1).setCellValue(quotation.getVendor().getCompanyName());
            row.createCell(2).setCellValue(quotation.getPurchaseRequest().getPrId());

            Cell dateCell = row.createCell(3);
            dateCell.setCellValue(quotation.getSubmissionDate());
            dateCell.setCellStyle(dateStyle);

            row.createCell(4).setCellValue(quotation.getTotalCost());
            row.createCell(5).setCellValue(quotation.getStatus());
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Shipbuilders Item export methods
    public void exportShipbuildersItemsToPdf(List<ShipbuildersItemEntity> items, HttpServletResponse response) throws DocumentException, IOException {
        Document document = new Document(PageSize.A2.rotate(), 20f, 20f, 20f, 20f);
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=items.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Shipbuilders Items", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(17);
        table.setWidthPercentage(100);
        table.setWidths(new float[]{
                4f, 5f, 6f, 4f, 5f,
                5f, 3f, 5f, 3f,
                6f, 3f, 3f,
                3f, 3f, 3f, 3f, 3f
        });
        // Add table headers
        String[] headers = {
                "Item Name", "Category (Product Stage)", "Sub Category (Material Family)", "Quality Check",
                "Item Code Gemba Suggested", "Specification 1 (Size)", "Dropdown", "Specification 2 (Unique)",
                "Specification 3", "Material Description / Product Name", "Material", "Purchase UOM",
                "Inventory UOM", "Qty/Pack", "Reorder Level", "Last Rate", "GST Slab"
        };
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD);
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
            cell.setNoWrap(true);
            cell.setFixedHeight(35f);
            cell.setPadding(6);
            table.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (ShipbuildersItemEntity item : items) {
            table.addCell(new Phrase(nullValue(item.getItemName()), dataFont));
            table.addCell(new Phrase(nullValue(item.getCategory()), dataFont));
            table.addCell(new Phrase(nullValue(item.getSubCategory()), dataFont));
            table.addCell(new Phrase(nullValue(item.getQualityCheck()), dataFont));
            table.addCell(new Phrase(nullValue(item.getItemCode()), dataFont));
            table.addCell(new Phrase(nullValue(item.getSpecification1()), dataFont));
            table.addCell(new Phrase(nullValue(item.getDropdown()), dataFont));
            table.addCell(new Phrase(nullValue(item.getSpecification2()), dataFont));
            table.addCell(new Phrase(nullValue(item.getSpecification3()), dataFont));
            table.addCell(new Phrase(nullValue(item.getMaterialDescription()), dataFont));
            table.addCell(new Phrase(nullValue(item.getMaterial()), dataFont));
            table.addCell(new Phrase(nullValue(item.getPurchasingUOM()), dataFont));
            table.addCell(new Phrase(nullValue(item.getInventoryUOM()), dataFont));
            table.addCell(new Phrase(numberToString(item.getQtyPerPack()), dataFont));
            table.addCell(new Phrase(numberToString(item.getReorderLevel()), dataFont));
            table.addCell(new Phrase(numberToString(item.getLastRate()), dataFont));
            table.addCell(new Phrase(numberToString(item.getGstSlab()), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportShipbuildersItemsToExcel(List<ShipbuildersItemEntity> items, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=shipbuilders_items.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Shipbuilders Items");

        // Create header style (bold, white bg, centered)
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setWrapText(true);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        String[] headers = {
                "Item Name", "Category (Product Stage)", "Sub Category (Material Family)", "Quality Check",
                "Item Code Gemba Suggested", "Specification 1 (Size)", "Dropdown", "Specification 2 (Unique Specification)",
                "Specification 3", "Material Description / Product Name", "Material", "Purchase UOM",
                "Inventory UOM", "Qty/Pack", "Reorder Level", "Last Rate", "GST Slab"
        };

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(30);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // Fill data rows
        int rowNum = 1;
        for (ShipbuildersItemEntity item : items) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(nullValue(item.getItemName()));
            row.createCell(1).setCellValue(nullValue(item.getCategory()));
            row.createCell(2).setCellValue(nullValue(item.getSubCategory()));
            row.createCell(3).setCellValue(nullValue(item.getQualityCheck()));
            row.createCell(4).setCellValue(nullValue(item.getItemCode()));
            row.createCell(5).setCellValue(nullValue(item.getSpecification1()));
            row.createCell(6).setCellValue(nullValue(item.getDropdown()));
            row.createCell(7).setCellValue(nullValue(item.getSpecification2()));
            row.createCell(8).setCellValue(nullValue(item.getSpecification3()));
            row.createCell(9).setCellValue(nullValue(item.getMaterialDescription()));
            row.createCell(10).setCellValue(nullValue(item.getMaterial()));
            row.createCell(11).setCellValue(nullValue(item.getPurchasingUOM()));
            row.createCell(12).setCellValue(nullValue(item.getInventoryUOM()));
            row.createCell(13).setCellValue(getDouble(item.getQtyPerPack()));
            row.createCell(14).setCellValue(getDouble(item.getReorderLevel()));
            row.createCell(15).setCellValue(getDouble(item.getLastRate()));
            row.createCell(16).setCellValue(getDouble(item.getGstSlab()));
        }

        // Autosize columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Vendor export methods
    public void exportVendorsToPdf(List<VendorEntity> vendors, HttpServletResponse response) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=vendors.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Vendors", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(new float[]{1, 2, 2, 2, 2, 2});
        table.setWidthPercentage(100);

        // Add table headers
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"ID", "Company Name", "Vendor Name", "Contact", "Email", "Vendor Type"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (VendorEntity vendor : vendors) {
            table.addCell(new Phrase(String.valueOf(vendor.getSrNo()), dataFont));
            table.addCell(new Phrase(vendor.getCompanyName(), dataFont));
            table.addCell(new Phrase(vendor.getVendorName(), dataFont));
            table.addCell(new Phrase(vendor.getContactNumber(), dataFont));
            table.addCell(new Phrase(vendor.getEmailId(), dataFont));
            table.addCell(new Phrase(vendor.getVendorType(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportVendorsToExcel(List<VendorEntity> vendors, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=vendors.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Vendors");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"ID", "Company Name", "Vendor Name", "Contact", "Email", "Vendor Type"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        for (VendorEntity vendor : vendors) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(vendor.getSrNo());
            row.createCell(1).setCellValue(vendor.getCompanyName());
            row.createCell(2).setCellValue(vendor.getVendorName());
            row.createCell(3).setCellValue(vendor.getContactNumber());
            row.createCell(4).setCellValue(vendor.getEmailId());
            row.createCell(5).setCellValue(vendor.getVendorType());
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // Contractor export methods
    public void exportContractorsToPdf(List<ContractorEntity> contractors, HttpServletResponse response) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=contractors.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Contractors", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table
        PdfPTable table = new PdfPTable(new float[]{1, 2, 2, 2, 2, 2});
        table.setWidthPercentage(100);

        // Add table headers
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"ID", "Contractor Name", "Contact", "Email", "Contractor Type", "Country Type"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (ContractorEntity contractor : contractors) {
            table.addCell(new Phrase(String.valueOf(contractor.getSrNo()), dataFont));
            table.addCell(new Phrase(contractor.getVendorName(), dataFont));
            table.addCell(new Phrase(contractor.getContactNumber(), dataFont));
            table.addCell(new Phrase(contractor.getEmailId(), dataFont));
            table.addCell(new Phrase(contractor.getVendorType(), dataFont));
            table.addCell(new Phrase(contractor.getContractorCountryType(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportContractorsToExcel(List<ContractorEntity> contractors, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=contractors.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Contractors");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"ID", "Contractor Name", "Contact", "Email", "Contractor Type", "Country Type"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        for (ContractorEntity contractor : contractors) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(contractor.getSrNo());
            row.createCell(1).setCellValue(contractor.getVendorName());
            row.createCell(2).setCellValue(contractor.getContactNumber());
            row.createCell(3).setCellValue(contractor.getEmailId());
            row.createCell(4).setCellValue(contractor.getVendorType());
            row.createCell(5).setCellValue(contractor.getContractorCountryType());
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    public void exportRegularPRsToPdf(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response)
            throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=regular_purchase_requests.pdf");

        PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Regular Purchase Requests", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        PdfPTable table = new PdfPTable(new float[]{1, 2, 3, 2, 2, 1, 1});
        table.setWidthPercentage(100);

        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (PurchaseRequestEntity pr : purchaseRequests) {
            table.addCell(new Phrase(pr.getPrId(), dataFont));
            table.addCell(new Phrase(dateFormat.format(pr.getCreatedDate()), dataFont));
            table.addCell(new Phrase(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName(), dataFont));
            table.addCell(new Phrase(pr.getYardNumber(), dataFont));
            table.addCell(new Phrase(pr.getProjectName(), dataFont));
            table.addCell(new Phrase(String.valueOf(pr.getLineItems().size()), dataFont));
            table.addCell(new Phrase(pr.getStatus(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportRegularPRsToExcel(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response)
            throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=regular_purchase_requests.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Regular Purchase Requests");

        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (PurchaseRequestEntity pr : purchaseRequests) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(pr.getPrId());

            Cell dateCell = row.createCell(1);
            dateCell.setCellValue(pr.getCreatedDate());
            dateCell.setCellStyle(dateStyle);

            row.createCell(2).setCellValue(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName());
            row.createCell(3).setCellValue(pr.getYardNumber());
            row.createCell(4).setCellValue(pr.getProjectName());
            row.createCell(5).setCellValue(pr.getLineItems().size());
            row.createCell(6).setCellValue(pr.getStatus());
        }

        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }


    public void exportPRsToPdfByStatus(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response)
            throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=regular_purchase_requests.pdf");

        PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
        Paragraph title = new Paragraph("Regular Purchase Requests", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        PdfPTable table = new PdfPTable(new float[]{1, 2, 3, 2, 2, 1, 1});
        table.setWidthPercentage(100);

        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        for (PurchaseRequestEntity pr : purchaseRequests) {
            table.addCell(new Phrase(pr.getPrId(), dataFont));
            table.addCell(new Phrase(dateFormat.format(pr.getCreatedDate()), dataFont));
            table.addCell(new Phrase(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName(), dataFont));
            table.addCell(new Phrase(pr.getYardNumber(), dataFont));
            table.addCell(new Phrase(pr.getProjectName(), dataFont));
            table.addCell(new Phrase(String.valueOf(pr.getLineItems().size()), dataFont));
            table.addCell(new Phrase(pr.getStatus(), dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportPRsToExcelByStatus(List<PurchaseRequestEntity> purchaseRequests, HttpServletResponse response)
            throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=regular_purchase_requests.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Regular Purchase Requests");

        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"PR ID", "Date", "Contractor Name", "Yard Number", "Project Name", "Line Items", "Status"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (PurchaseRequestEntity pr : purchaseRequests) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue(pr.getPrId());

            Cell dateCell = row.createCell(1);
            dateCell.setCellValue(pr.getCreatedDate());
            dateCell.setCellStyle(dateStyle);

            row.createCell(2).setCellValue(pr.getContractors().isEmpty() ? "" : pr.getContractors().get(0).getVendorName());
            row.createCell(3).setCellValue(pr.getYardNumber());
            row.createCell(4).setCellValue(pr.getProjectName());
            row.createCell(5).setCellValue(pr.getLineItems().size());
            row.createCell(6).setCellValue(pr.getStatus());
        }

        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    public void exportPurchaseRequestLightDTOsToPdf(List<PurchaseRequestLightDTO> prList, HttpServletResponse response) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4.rotate());
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_requests_dashboard.pdf");
        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add title
        com.itextpdf.text.Font titleFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 18, com.itextpdf.text.Font.BOLD);
        Paragraph title = new Paragraph("Purchase Requests", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(20);
        document.add(title);

        // Create table with columns: ID, PR ID, Date, Client Name, Yard Number, Project Name, Line Items, Status, PR Type
        PdfPTable table = new PdfPTable(new float[]{1, 2, 2, 3, 2, 2, 1, 2, 2});
        table.setWidthPercentage(100);

        // Add table headers
        com.itextpdf.text.Font headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 10, com.itextpdf.text.Font.BOLD);
        String[] headers = {"ID", "PR ID", "Date", "Client Name", "Yard Number", "Project Name", "Line Items", "Status", "PR Type"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            table.addCell(cell);
        }

        // Add data rows
        com.itextpdf.text.Font dataFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, 9, com.itextpdf.text.Font.NORMAL);
        for (PurchaseRequestLightDTO pr : prList) {
            table.addCell(new Phrase(pr.getId() != null ? pr.getId().toString() : "", dataFont));
            table.addCell(new Phrase(pr.getPrId() != null ? pr.getPrId() : "", dataFont));
            table.addCell(new Phrase(pr.getCreatedDate() != null ? dateFormat.format(pr.getCreatedDate()) : "", dataFont));
            table.addCell(new Phrase(pr.getClientName() != null ? pr.getClientName() : "", dataFont));
            table.addCell(new Phrase(pr.getYardNumber() != null ? pr.getYardNumber() : "", dataFont));
            table.addCell(new Phrase(pr.getProjectName() != null ? pr.getProjectName() : "", dataFont));
            table.addCell(new Phrase(pr.getLineItemCount() != null ? pr.getLineItemCount().toString() : "", dataFont));
            table.addCell(new Phrase(pr.getStatus() != null ? pr.getStatus() : "", dataFont));
            table.addCell(new Phrase(pr.getPrType() != null ? pr.getPrType() : "", dataFont));
        }

        document.add(table);
        document.close();
    }

    public void exportPurchaseRequestLightDTOsToExcel(List<PurchaseRequestLightDTO> prList, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_requests_dashboard.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Purchase Requests");

        // Create header row
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        String[] columns = {"ID", "PR ID", "Date", "Client Name", "Yard Number", "Project Name", "Line Items", "Status", "PR Type"};
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerStyle);
        }

        // Create data rows
        int rowNum = 1;
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat("dd-MM-yyyy"));

        for (PurchaseRequestLightDTO pr : prList) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(pr.getId() != null ? pr.getId() : 0);
            row.createCell(1).setCellValue(pr.getPrId() != null ? pr.getPrId() : "");
            Cell dateCell = row.createCell(2);
            if (pr.getCreatedDate() != null) {
                dateCell.setCellValue(pr.getCreatedDate());
                dateCell.setCellStyle(dateStyle);
            }
            row.createCell(3).setCellValue(pr.getClientName() != null ? pr.getClientName() : "");
            row.createCell(4).setCellValue(pr.getYardNumber() != null ? pr.getYardNumber() : "");
            row.createCell(5).setCellValue(pr.getProjectName() != null ? pr.getProjectName() : "");
            row.createCell(6).setCellValue(pr.getLineItemCount() != null ? pr.getLineItemCount() : 0);
            row.createCell(7).setCellValue(pr.getStatus() != null ? pr.getStatus() : "");
            row.createCell(8).setCellValue(pr.getPrType() != null ? pr.getPrType() : "");
        }

        // Autosize columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * Generates a Purchase Order PDF for a specific purchase request
     *
     * @param purchaseRequest The purchase request entity
     * @param deliveryTerms The delivery terms from the form
     * @param response The HTTP response
     * @throws DocumentException If there's an issue creating the PDF
     * @throws IOException If there's an I/O error
     */
    public void generatePurchaseOrderPdf(PurchaseRequestEntity purchaseRequest,
                                         PurchaseOrderDeliveryTermsDTO deliveryTerms,
                                         HttpServletResponse response) throws DocumentException, IOException {
        com.itextpdf.text.Document document = new com.itextpdf.text.Document(PageSize.A4);
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_order_" + purchaseRequest.getPrId() + ".pdf");

        // Update PR items with rates from selected quotation items
        updatePRItemsWithSelectedQuotationRates(purchaseRequest);

        PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
        document.open();

        // Add company logo and header
        addCompanyHeader(document);

        // Add order information table
        addOrderInfoTable(document, purchaseRequest);

        // Add addresses section
        addAddressesSection(document, purchaseRequest, deliveryTerms);

        // Add terms section
        addTermsSection(document, deliveryTerms);

        // Add introduction text
        Font normalFont = new Font(Font.FontFamily.HELVETICA, 10, Font.NORMAL);
        Paragraph intro = new Paragraph("Dear Sir,\n\nAs per your quotation, we are pleased to place our Purchase Order as per the following:", normalFont);
        intro.setSpacingBefore(10);
        intro.setSpacingAfter(10);
        document.add(intro);

        // Add items table
        addItemsTable(document, purchaseRequest, deliveryTerms);

        // Add footer with additional notes and company details
        addFooter(document);

        // Add terms and conditions page (single page with smaller font)
        document.newPage();
        addTermsAndConditionsSinglePage(document);

        document.close();
    }

    /**
     * Adds the company header to the PDF
     */
    private void addCompanyHeader(com.itextpdf.text.Document document) throws DocumentException, IOException {
        // Create a compact centered header layout like the letterhead design
        Font companyNameFont = new Font(Font.FontFamily.HELVETICA, 16, Font.BOLD, BaseColor.RED);
        Font detailsFont = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.BLACK);
        Font addressFont = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL, BaseColor.BLACK);
        Font boldFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD, BaseColor.BLACK);

        // Create a centered table for logo and company info
        PdfPTable headerTable = new PdfPTable(2);
        headerTable.setWidthPercentage(60); // Centered block, not full width
        headerTable.setHorizontalAlignment(Element.ALIGN_CENTER);
        headerTable.setWidths(new float[]{1, 3}); // Logo and company text proportions

        // Logo cell
        PdfPCell logoCell = new PdfPCell();
        logoCell.setBorderColor(BaseColor.WHITE);
        logoCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        logoCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        logoCell.setPadding(5);

        try {
            Image logo = null;

            // Try to load from file system first
            try {
                File logoFile = new File("uploads/synergy-logo.jpeg");
                if (logoFile.exists()) {
                    logo = Image.getInstance(logoFile.getAbsolutePath());
                    System.out.println("Logo loaded from: " + logoFile.getAbsolutePath());
                }
            } catch (Exception e1) {
                System.out.println("Failed to load logo from uploads directory: " + e1.getMessage());
            }

            // Try to load from classpath resources
            if (logo == null) {
                try {
                    URL resource = getClass().getClassLoader().getResource("static/images/synergy-logo.jpeg");
                    if (resource != null) {
                        logo = Image.getInstance(resource);
                        System.out.println("Logo loaded from classpath: " + resource.getPath());
                    }
                } catch (Exception e2) {
                    System.out.println("Failed to load logo from classpath: " + e2.getMessage());
                }
            }

            if (logo != null) {
                logo.scaleToFit(60, 45);
                logo.setAlignment(Element.ALIGN_CENTER);
                logoCell.addElement(logo);
            } else {
                // Fallback placeholder
                Paragraph logoPlaceholder = new Paragraph("[LOGO]", detailsFont);
                logoPlaceholder.setAlignment(Element.ALIGN_CENTER);
                logoCell.addElement(logoPlaceholder);
            }
        } catch (Exception e) {
            System.out.println("Failed to load logo: " + e.getMessage());
            Paragraph logoPlaceholder = new Paragraph("[LOGO]", detailsFont);
            logoPlaceholder.setAlignment(Element.ALIGN_CENTER);
            logoCell.addElement(logoPlaceholder);
        }

        headerTable.addCell(logoCell);

        // Company info cell
        PdfPCell companyInfoCell = new PdfPCell();
        companyInfoCell.setBorderColor(BaseColor.WHITE);
        companyInfoCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        companyInfoCell.setPadding(5);

        // Company name - split into lines like the letterhead
        Paragraph companyName1 = new Paragraph("SYNERGY", companyNameFont);
        companyName1.setAlignment(Element.ALIGN_LEFT);
        companyName1.setSpacingAfter(0);
        companyInfoCell.addElement(companyName1);

        Paragraph companyName2 = new Paragraph("SHIPBUILDERS", companyNameFont);
        companyName2.setAlignment(Element.ALIGN_LEFT);
        companyName2.setSpacingAfter(0);
        companyInfoCell.addElement(companyName2);

        Paragraph companyName3 = new Paragraph("N DOCK WORKS LIMITED", companyNameFont);
        companyName3.setAlignment(Element.ALIGN_LEFT);
        companyInfoCell.addElement(companyName3);

        headerTable.addCell(companyInfoCell);
        document.add(headerTable);

        // Add registered office and legal information centered below
        Paragraph registeredOffice = new Paragraph();
        registeredOffice.add(new Chunk("Registered Office: ", boldFont));
        registeredOffice.add(new Chunk("Silver Den, Sea View Angel Vihar Bagmulla, Vasco - Goa, India - 403 806.", addressFont));
        registeredOffice.setAlignment(Element.ALIGN_CENTER);
        registeredOffice.setSpacingBefore(5);
        registeredOffice.setSpacingAfter(2);
        document.add(registeredOffice);

        Paragraph legalInfo = new Paragraph();
        legalInfo.add(new Chunk("CIN: U03113GA2025PC017015  |  GST No: 30ABFCS0272H1ZG", addressFont));
        legalInfo.setAlignment(Element.ALIGN_CENTER);
        legalInfo.setSpacingAfter(5);
        document.add(legalInfo);

        // Add a line separator
        LineSeparator line = new LineSeparator();
        line.setLineColor(BaseColor.BLACK);
        document.add(new Chunk(line));
        document.add(new Paragraph(" ", new Font(Font.FontFamily.HELVETICA, 4))); // Very small space after line
    }

    /**
     * Adds the order information table to the PDF
     */
    private void addOrderInfoTable(com.itextpdf.text.Document document, PurchaseRequestEntity purchaseRequest) throws DocumentException {
        PdfPTable orderTable = new PdfPTable(1);
        orderTable.setWidthPercentage(100);

        // ORDER header - extended as a column header
        PdfPCell orderHeaderCell = new PdfPCell();
        orderHeaderCell.setBorderWidth(1);
        orderHeaderCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        orderHeaderCell.setBackgroundColor(BaseColor.BLACK);
        Font orderHeaderFont = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD, BaseColor.WHITE);
        orderHeaderCell.setPhrase(new Phrase("ORDER", orderHeaderFont));
        orderTable.addCell(orderHeaderCell);

        // Get current financial year in YY-YY format (still needed for order number)
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1; // Calendar months are 0-based
        String financialYear;
        if (month >= 4) { // April onwards is new financial year in India
            // Format as YY-YY (e.g., 25-26 instead of 2025-2026)
            financialYear = String.format("%02d-%02d", year % 100, (year + 1) % 100);
        } else {
            financialYear = String.format("%02d-%02d", (year - 1) % 100, year % 100);
        }

        // Create a table for PO details with 2 columns
        PdfPTable poDetailsTable = new PdfPTable(2);
        poDetailsTable.setWidthPercentage(100);
        poDetailsTable.setWidths(new float[]{1, 1});

        // Purchase Order #
        PdfPCell poLabelCell = new PdfPCell(new Phrase("Purchase Order #"));
        poLabelCell.setBorderWidth(1);
        poDetailsTable.addCell(poLabelCell);

        // Date Created
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        PdfPCell dateCreatedCell = new PdfPCell(new Phrase("Date Created"));
        dateCreatedCell.setBorderWidth(1);
        poDetailsTable.addCell(dateCreatedCell);

        // Generate order number in format: SS/[Vendor Initials]/[PR ID]/[Financial Year]
        String vendorInitials = "";
        if (purchaseRequest.getVendors() != null && !purchaseRequest.getVendors().isEmpty()) {
            VendorEntity vendor = purchaseRequest.getVendors().get(0);
            // Get initials from vendor name or company name
            String vendorName = vendor.getVendorName() != null ? vendor.getVendorName() :
                              (vendor.getCompanyName() != null ? vendor.getCompanyName() : "");

            if (!vendorName.isEmpty()) {
                // Extract initials (first letter of each word)
                String[] words = vendorName.split("\\s+");
                StringBuilder initialsBuilder = new StringBuilder();
                for (String word : words) {
                    if (!word.isEmpty()) {
                        initialsBuilder.append(word.charAt(0));
                    }
                }
                vendorInitials = initialsBuilder.toString().toUpperCase();
            }
        }

        // If no vendor initials, use "XX" as fallback
        if (vendorInitials.isEmpty()) {
            vendorInitials = "XX";
        }

        // Format the order number using the numeric ID (database ID) instead of PR ID
        String orderNumber = "SS/" + vendorInitials + "/" + purchaseRequest.getId() + "/" + financialYear;

        // Order number value
        PdfPCell poValueCell = new PdfPCell(new Phrase(orderNumber));
        poValueCell.setBorderWidth(1);
        poDetailsTable.addCell(poValueCell);

        // Date value
        PdfPCell dateValueCell = new PdfPCell(new Phrase(dateFormat.format(purchaseRequest.getCreatedDate())));
        dateValueCell.setBorderWidth(1);
        poDetailsTable.addCell(dateValueCell);

        // Note about quoting order number - add to inner table spanning both columns
        PdfPCell noteCell = new PdfPCell(new Phrase("Please quote the order no. for all future correspondents and invoices"));
        noteCell.setBorderWidth(1);
        noteCell.setColspan(2); // Span across both columns of the inner table
        poDetailsTable.addCell(noteCell);

        // Add the PO details table to the main order table
        PdfPCell poDetailsCell = new PdfPCell();
        poDetailsCell.setBorderWidth(0);
        poDetailsCell.addElement(poDetailsTable);
        orderTable.addCell(poDetailsCell);

        document.add(orderTable);
    }

    /**
     * Adds the addresses section to the PDF
     */
    private void addAddressesSection(Document document, PurchaseRequestEntity purchaseRequest,
                                    PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        PdfPTable addressTable = new PdfPTable(2);
        addressTable.setWidthPercentage(100);
        addressTable.setSpacingBefore(10);

        // Supplier details
        PdfPCell supplierHeaderCell = new PdfPCell(new Phrase("SUPPLIER DETAILS:"));
        supplierHeaderCell.setBorderWidth(1);
        supplierHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(supplierHeaderCell);

        // Invoice address
        PdfPCell invoiceHeaderCell = new PdfPCell(new Phrase("INVOICE ADDRESS - Bill To:"));
        invoiceHeaderCell.setBorderWidth(1);
        invoiceHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(invoiceHeaderCell);

        // Supplier details content - this would be the vendor details from the PR
        String supplierDetails = "";
        if (purchaseRequest.getVendors() != null && !purchaseRequest.getVendors().isEmpty()) {
            VendorEntity vendor = purchaseRequest.getVendors().get(0);
            supplierDetails = vendor.getCompanyName();

            // For now, just use a simple address to avoid JSON parsing issues
            // This is a temporary solution until the vendor address format is standardized
            if (vendor.getCity() != null) {
                supplierDetails += "\n" + vendor.getCity();
            }
            if (vendor.getState() != null) {
                supplierDetails += ", " + vendor.getState();
            }
            if (vendor.getCountry() != null) {
                supplierDetails += ", " + vendor.getCountry();
            }

            if (vendor.getContactNumber() != null) {
                supplierDetails += "\nPhone: " + vendor.getContactNumber();
            }

            if (vendor.getEmailId() != null) {
                supplierDetails += "\nEmail: " + vendor.getEmailId();
            }
        }

        PdfPCell supplierDetailsCell = new PdfPCell(new Phrase(supplierDetails));
        supplierDetailsCell.setBorderWidth(1);
        supplierDetailsCell.setMinimumHeight(60);
        addressTable.addCell(supplierDetailsCell);

        // Get delivery address from the form
        String deliveryAddress = deliveryTerms.getDeliveryLocation();

        // Format the delivery address if needed
        if (deliveryAddress == null || deliveryAddress.trim().isEmpty()) {
            deliveryAddress = "Synergy Shipbuilders\n" +
                             "Silver Den Sea View Anant Vihar,\n" +
                             "Bogmalo, Vasco, Goa – India, 403802";
        }

        // Invoice address content - same as delivery address
        String invoiceAddress = deliveryAddress;

        PdfPCell invoiceAddressCell = new PdfPCell(new Phrase(invoiceAddress));
        invoiceAddressCell.setBorderWidth(1);
        invoiceAddressCell.setMinimumHeight(60);
        addressTable.addCell(invoiceAddressCell);

        // Sold to
        PdfPCell soldToHeaderCell = new PdfPCell(new Phrase("SOLD TO:"));
        soldToHeaderCell.setBorderWidth(1);
        soldToHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(soldToHeaderCell);

        // Delivery address
        PdfPCell deliveryHeaderCell = new PdfPCell(new Phrase("DELIVERY ADDRESS - Ship To:"));
        deliveryHeaderCell.setBorderWidth(1);
        deliveryHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(deliveryHeaderCell);

        // Sold to content - hardcoded as per requirements
        String soldToAddress = "Synergy Shipbuilders\n" +
                              "Yard: Gauaudaulim, Cumbarjua, Tiswadi, Ilhas\n" +
                              "Goa – India\n" +
                              "GST: 30ADKFS3803J1Z7";

        PdfPCell soldToCell = new PdfPCell(new Phrase(soldToAddress));
        soldToCell.setBorderWidth(1);
        soldToCell.setMinimumHeight(60);
        addressTable.addCell(soldToCell);

        // Delivery address content - already defined above
        PdfPCell deliveryAddressCell = new PdfPCell(new Phrase(deliveryAddress));
        deliveryAddressCell.setBorderWidth(1);
        deliveryAddressCell.setMinimumHeight(60);
        addressTable.addCell(deliveryAddressCell);

        document.add(addressTable);
    }

    /**
     * Adds the terms section to the PDF
     */
    private void addTermsSection(Document document, PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        PdfPTable termsTable = new PdfPTable(4);
        termsTable.setWidthPercentage(100);
        termsTable.setSpacingBefore(10);

        // Currency header
        PdfPCell currencyHeaderCell = new PdfPCell(new Phrase("CURRENCY:"));
        currencyHeaderCell.setBorderWidth(1);
        currencyHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        termsTable.addCell(currencyHeaderCell);

        // Payment terms header
        PdfPCell paymentTermsHeaderCell = new PdfPCell(new Phrase("PAYMENT TERMS:"));
        paymentTermsHeaderCell.setBorderWidth(1);
        paymentTermsHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        termsTable.addCell(paymentTermsHeaderCell);

        // Delivery date header
        PdfPCell deliveryDateHeaderCell = new PdfPCell(new Phrase("DELIVERY DATE:"));
        deliveryDateHeaderCell.setBorderWidth(1);
        deliveryDateHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        termsTable.addCell(deliveryDateHeaderCell);

        // Delivery terms header
        PdfPCell deliveryTermsHeaderCell = new PdfPCell(new Phrase("DELIVERY TERMS:"));
        deliveryTermsHeaderCell.setBorderWidth(1);
        deliveryTermsHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        termsTable.addCell(deliveryTermsHeaderCell);

        // Currency content
        PdfPCell currencyCell = new PdfPCell(new Phrase(deliveryTerms.getCurrency()));
        currencyCell.setBorderWidth(1);
        termsTable.addCell(currencyCell);

        // Payment terms content
        String paymentTerms = deliveryTerms.getAdvancePaymentPercentage() + "% Advance";
        PdfPCell paymentTermsCell = new PdfPCell(new Phrase(paymentTerms));
        paymentTermsCell.setBorderWidth(1);
        termsTable.addCell(paymentTermsCell);

        // Delivery date content
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        String deliveryDate = dateFormat.format(deliveryTerms.getDeliveryDate());
        PdfPCell deliveryDateCell = new PdfPCell(new Phrase(deliveryDate));
        deliveryDateCell.setBorderWidth(1);
        termsTable.addCell(deliveryDateCell);

        // Delivery terms content - from frontend dropdown selection
        String deliveryTermsText = deliveryTerms.getDeliveryTerms();
        if (deliveryTermsText == null || deliveryTermsText.trim().isEmpty()) {
            deliveryTermsText = "As per agreement"; // Fallback for backward compatibility
        }
        PdfPCell deliveryTermsCell = new PdfPCell(new Phrase(deliveryTermsText));
        deliveryTermsCell.setBorderWidth(1);
        termsTable.addCell(deliveryTermsCell);

        document.add(termsTable);
    }

    /**
     * Updates the PR items with rates from selected quotation items
     */
    private void updatePRItemsWithSelectedQuotationRates(PurchaseRequestEntity purchaseRequest) {
        try {
            // Get all quotations for this PR
            List<VendorQuotationEntity> allQuotations = vendorQuotationRepository.findByPurchaseRequestId(purchaseRequest.getId());

            if (allQuotations.isEmpty()) {
                System.out.println("WARNING: No quotations found for PR " + purchaseRequest.getId());
                return;
            }

            // Get all selected quotation items
            List<VendorQuotationItemEntity> selectedItems = allQuotations.stream()
                .flatMap(quotation -> quotation.getItems().stream())
                .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                .collect(Collectors.toList());

            if (selectedItems.isEmpty()) {
                System.out.println("WARNING: No selected quotation items found for PR " + purchaseRequest.getId());
                return;
            }

            System.out.println("DEBUG: Found " + selectedItems.size() + " selected quotation items for PR " + purchaseRequest.getId());

            // Update PR items with rates from selected quotation items
            for (PurchaseRequestItemEntity prItem : purchaseRequest.getLineItems()) {
                // Find matching selected quotation item by uniqueCode
                selectedItems.stream()
                    .filter(quotationItem -> quotationItem.getUniqueCode().equals(prItem.getUniqueCode()))
                    .findFirst()
                    .ifPresent(selectedItem -> {
                        // Update rate and total
                        BigDecimal rate = BigDecimal.valueOf(selectedItem.getUnitPrice());
                        prItem.setRate(rate);

                        // Calculate total (quantity * rate)
                        BigDecimal quantity = new BigDecimal(prItem.getQuantity());
                        BigDecimal total = quantity.multiply(rate);
                        prItem.setTotal(total);

                        System.out.println("DEBUG: Updated PR item " + prItem.getProductName() +
                                          " with rate " + rate + " from selected quotation");
                    });
            }
        } catch (Exception e) {
            System.out.println("ERROR: Failed to update PR items with selected quotation rates: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Adds the items table to the PDF
     */
    private void addItemsTable(Document document, PurchaseRequestEntity purchaseRequest,
                              PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        // Debug log
        System.out.println("DEBUG: Purchase Request Items:");
        if (purchaseRequest.getLineItems() != null) {
            for (PurchaseRequestItemEntity item : purchaseRequest.getLineItems()) {
                System.out.println("Item: " + item.getProductName() +
                                  ", Qty: " + item.getQuantity() +
                                  ", Rate: " + (item.getRate() != null ? item.getRate() : "null") +
                                  ", Total: " + (item.getTotal() != null ? item.getTotal() : "null"));
            }
        }

        PdfPTable itemsTable = new PdfPTable(5);
        itemsTable.setWidthPercentage(100);
        itemsTable.setSpacingBefore(10);
        itemsTable.setWidths(new float[]{0.5f, 3, 1, 1, 1.5f});

        // Add table headers
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
        String[] headers = {"Sr. No", "Description", "Qty", "Unit Rate", "Total amount"};
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(cell);
        }

        // Add data rows
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);
        BigDecimal totalAmount = BigDecimal.ZERO;

        int srNo = 1;
        for (PurchaseRequestItemEntity item : purchaseRequest.getLineItems()) {
            // Sr. No
            PdfPCell srNoCell = new PdfPCell(new Phrase(String.valueOf(srNo++), dataFont));
            srNoCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(srNoCell);

            // Description
            PdfPCell descriptionCell = new PdfPCell(new Phrase(item.getProductName(), dataFont));
            itemsTable.addCell(descriptionCell);

            // Quantity
            PdfPCell qtyCell = new PdfPCell(new Phrase(String.valueOf(item.getQuantity()), dataFont));
            qtyCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(qtyCell);

            // Unit Rate
            BigDecimal rate = item.getRate() != null ? item.getRate() : BigDecimal.ZERO;
            // Format to 2 decimal places
            rate = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
            PdfPCell rateCell = new PdfPCell(new Phrase(rate.toString(), dataFont));
            rateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            itemsTable.addCell(rateCell);

            // Calculate item total (quantity * rate)
            BigDecimal itemTotal;
            if (item.getTotal() != null && item.getTotal().compareTo(BigDecimal.ZERO) > 0) {
                itemTotal = item.getTotal();
            } else {
                // Calculate if total is not set
                BigDecimal quantity = new BigDecimal(item.getQuantity());
                // Use the already defined rate variable from above
                itemTotal = quantity.multiply(rate);
            }

            // Format to 2 decimal places
            itemTotal = itemTotal.setScale(2, BigDecimal.ROUND_HALF_UP);

            // Debug log for item total calculation
            System.out.println("DEBUG: Calculated item total for " + item.getProductName() +
                              ": Qty=" + item.getQuantity() +
                              " * Rate=" + rate +
                              " = " + itemTotal);

            // Total amount
            PdfPCell totalCell = new PdfPCell(new Phrase(itemTotal.toString(), dataFont));
            totalCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            itemsTable.addCell(totalCell);

            totalAmount = totalAmount.add(itemTotal);
            System.out.println("DEBUG: Running total amount: " + totalAmount);
        }

        // Add empty rows if needed to make the table look better
        while (srNo <= 4) {
            for (int i = 0; i < 5; i++) {
                itemsTable.addCell(new PdfPCell(new Phrase(" ")));
            }
            srNo++;
        }

        // Format total amount to 2 decimal places
        totalAmount = totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);

        // Add total amount row
        PdfPCell totalLabelCell = new PdfPCell(new Phrase("TOTAL AMOUNT", headerFont));
        totalLabelCell.setColspan(4);
        totalLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(totalLabelCell);

        PdfPCell totalValueCell = new PdfPCell(new Phrase(totalAmount.toString(), headerFont));
        totalValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(totalValueCell);

        // Add tax row
        BigDecimal taxPercentage;
        try {
            taxPercentage = new BigDecimal(deliveryTerms.getTaxPercentage());
        } catch (NumberFormatException e) {
            // Default to 0 if tax percentage is not a valid number
            taxPercentage = BigDecimal.ZERO;
        }

        BigDecimal taxAmount;
        try {
            taxAmount = totalAmount.multiply(taxPercentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
        } catch (ArithmeticException e) {
            // Handle division by zero or other arithmetic errors
            taxAmount = BigDecimal.ZERO;
        }

        // Format tax amount to 2 decimal places
        taxAmount = taxAmount.setScale(2, BigDecimal.ROUND_HALF_UP);

        // Debug log for tax calculation
        System.out.println("DEBUG: Tax calculation: " + totalAmount + " * " + taxPercentage + "% = " + taxAmount);

        PdfPCell taxLabelCell = new PdfPCell(new Phrase("TAX " + deliveryTerms.getTaxPercentage() + "%", headerFont));
        taxLabelCell.setColspan(4);
        taxLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(taxLabelCell);

        PdfPCell taxValueCell = new PdfPCell(new Phrase(taxAmount.toString(), headerFont));
        taxValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(taxValueCell);

        // Add grand total row
        BigDecimal grandTotal = totalAmount.add(taxAmount);
        // Format grand total to 2 decimal places
        grandTotal = grandTotal.setScale(2, BigDecimal.ROUND_HALF_UP);

        // Debug log for grand total calculation
        System.out.println("DEBUG: Grand total calculation: " + totalAmount + " + " + taxAmount + " = " + grandTotal);

        PdfPCell grandTotalLabelCell = new PdfPCell(new Phrase("GRAND TOTAL AMOUNT", headerFont));
        grandTotalLabelCell.setColspan(4);
        grandTotalLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(grandTotalLabelCell);

        PdfPCell grandTotalValueCell = new PdfPCell(new Phrase(grandTotal.toString(), headerFont));
        grandTotalValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        itemsTable.addCell(grandTotalValueCell);

        document.add(itemsTable);
    }

    /**
     * Adds the footer to the PDF
     */
    private void addFooter(Document document) throws DocumentException {
        // Add additional note
        PdfPTable noteTable = new PdfPTable(1);
        noteTable.setWidthPercentage(100);
        noteTable.setSpacingBefore(8);

        PdfPCell noteHeaderCell = new PdfPCell(new Phrase("Additional Note:"));
        noteHeaderCell.setBorderWidth(1);
        noteHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        noteTable.addCell(noteHeaderCell);

        String noteText = "In order to promote eco-friendly business and timely processing of payments, please submit digitally signed invoices.";
        PdfPCell noteTextCell = new PdfPCell(new Phrase(noteText));
        noteTextCell.setBorderWidth(1);
        noteTable.addCell(noteTextCell);

        document.add(noteTable);

        // Add company footer with compact spacing
        Font footerFont = new Font(Font.FontFamily.HELVETICA, 7, Font.NORMAL, BaseColor.BLACK);
        Font boldFooterFont = new Font(Font.FontFamily.HELVETICA, 7, Font.BOLD, BaseColor.BLACK);

        // Corporate Office
        Paragraph corporateOffice = new Paragraph();
        corporateOffice.setAlignment(Element.ALIGN_CENTER);
        corporateOffice.setSpacingBefore(8);
        corporateOffice.setSpacingAfter(1);
        corporateOffice.add(new Chunk("Corporate Office: ", boldFooterFont));
        corporateOffice.add(new Chunk("Epicentre by Wadhwa Building, Office no. 1012, 10th Floor, W T Patil Marg, Chembur, Mumbai - 400071.", footerFont));
        document.add(corporateOffice);

        // Branch and Site Yards
        Paragraph yards = new Paragraph();
        yards.setAlignment(Element.ALIGN_CENTER);
        yards.setSpacingAfter(1);
        yards.add(new Chunk("Mumbai Yard: ", boldFooterFont));
        yards.add(new Chunk("Hoy Bunder, Near LBS College, Sewri, Mumbai  |  ", footerFont));
        yards.add(new Chunk("Site Yard: ", boldFooterFont));
        yards.add(new Chunk("Gaundalim, Cumbarjua, Tiswadi, Ilhas, Goa, INDIA", footerFont));
        document.add(yards);

        // Contact Information
        Paragraph contact = new Paragraph();
        contact.setAlignment(Element.ALIGN_CENTER);
        contact.add(new Chunk("+*************  <EMAIL>  www.synergyshipbuilders.com", footerFont));
        document.add(contact);
    }

    /**
     * Adds the first terms and conditions page to the PDF
     */
    private void addTermsAndConditionsPage1(Document document) throws DocumentException {
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD);
        Font normalFont = new Font(Font.FontFamily.HELVETICA, 10, Font.NORMAL);

        // Title
        Paragraph title = new Paragraph("General Terms & Conditions of the Purchase Order", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(10);
        document.add(title);

        // Terms and conditions content - first 13 terms for page 1 (moved term 13 from page 2)
        List<String> terms = new ArrayList<>();
        terms.add("In this Purchase Order 'Buyer' means Synergy Shipbuilders who has issued the PO and the 'Supplier' shall mean the person/company/organization from whom the Goods and/or Services are being bought and these terms and conditions form an integral part of the PO.");
        terms.add("Acceptance: This PO constitutes the Buyer's final offer to the Supplier and it shall become a binding and enforceable agreement on acceptance from the supplier has to be conveyed to the buyer within 2 working days. PO status unless otherwise mentioned in the PO, for fixed and not subject to any escalation for any reasons whatsoever.");
        terms.add("Price: The prices mentioned in the PO are fixed and not subject to any escalation for any reasons whatsoever.");
        terms.add("Charges: All charges including packing, forwarding, loading and transportation shall be deemed as included in the PO rates unless otherwise mentioned in the PO. Any expenses on account of demurrage, wharfage or pilferage in transit shall be borne by the supplier. Unloading at buyer's site will be arranged by the buyer.");
        terms.add("Insurance: Unless otherwise mentioned in the PO, the expenses on account of arrangement and provision of transit insurance of goods for 100% of PO value up to unloading of goods at the delivery destination specified in the PO shall be borne by the supplier.");
        terms.add("Export and Import procedures: The company is fully responsible to arrange all the export documents prior to shipping of the goods. Unless/ until confirmed by the buyer's team all paperwork in full, the supplier is not allowed to release the goods.");
        terms.add("Delivery: The supplier agrees that timely delivery in accordance with the delivery schedule mentioned in the PO is the essence of the PO. Buyer further reserves the right to request to expedite the delivery of the goods if required. Any damages occurred in case of delays, the supplier is to be responsible and buyer reserves the right to cancel the order.");
        terms.add("Liquidated damages for delay: In case of delay in delivery beyond the agreed delivery date, the supplier shall be liable to pay liquidated damages at the rate of 0.5% of the PO value per week of delay, subject to a maximum of 5% of the PO value.");
        terms.add("Warranties & Guarantees: The supplier undertakes and guarantees that all goods are free from any defects and undertakes to provide all necessary Certificates & Guarantees as applicable. Any defect observed either in the course of inspection or during the warranty period shall be rectified by the supplier.");
        terms.add("Inspection / Rejection of Goods/ Services: Unless otherwise mentioned in the PO, all goods/services received are subject to final approval of the buyer and inspection regarding quality, quantity, and specifications. Any defect noticed at the time of assembling or processing, even if the first instance of goods have been accepted and paid for, the buyer reserves the right to reject the goods owing to manufacturing defects at any time.");
        terms.add("The buyer reserves the right to terminate or amend the PO or any part thereof for the following reasons, without the buyer incurring any liability due irregularities in the supply: rejection, escalation in the prices, not required by the buyer, risk of potential.");
        terms.add("The supplier holds the responsibility on informing the buyer of any irregularities in fulfilling the PO. Any excess items will be returned on the suppliers cost and any short items needs to be fulfilled on priority. The buyer reserves the right to withhold payment or terminate the PO. Any damages / delay occurred will be responsibility of the supplier.");
        terms.add("Intellectual Property Rights: The supplier shall ensure that the goods/services supplied are not infringing any and all intellectual Property Rights including but not limited to patents, copyrights, designs, and trademarks.");

        // Add terms to document
        int termNumber = 1;
        for (String term : terms) {
            Paragraph paragraph = new Paragraph(termNumber + ". " + term, normalFont);
            paragraph.setSpacingAfter(5);
            document.add(paragraph);
            termNumber++;
        }
    }

    /**
     * Adds the second terms and conditions page to the PDF
     */
    private void addTermsAndConditionsPage2(Document document) throws DocumentException {
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD);
        Font normalFont = new Font(Font.FontFamily.HELVETICA, 10, Font.NORMAL);

        // Add a small title to indicate continuation
        Paragraph title = new Paragraph("General Terms & Conditions (Continued)", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(10);
        document.add(title);

        // Terms and conditions content - remaining terms for second page (Terms 14-25)
        List<String> terms = new ArrayList<>();
        // Terms 14-25 (removed term 13 which is now on page 1)
        terms.add("Limitation of Buyers Liability: Under any situation, the buyer shall not be liable for any loss of profit, Incidental, indirect or consequential damages, irrespective if whether such claim is based on tort or otherwise. Buyers liability on any other kind of loss or damage arising out of or connected with this PO or the contract based on this or on the performance or otherwise thereof, shall in no case exceed a maximum of 5% of the price allocable to the goods/services thereof which give rise to such a claim and would be bound by the Law of Limitation.");
        terms.add("Confidentiality and Publicity: All drawings, designs, jigs, tools, patterns, and samples, if any provided by the buyer to the supplier in respect of the PO are to be treated as confidential and shall be used only for the purpose of performance of the PO and shall be an exclusive property of the buyer. Under any circumstances the supplier shall not reproduce or disclose in whole or part or any other purpose to third party(s) without prior consent in writing from buyer. Both the parties are bound by this confidentiality clause, as implied by law.");
        terms.add("Indemnity: The supplier shall indemnify, defend and hold harmless the buyer or affiliates to any claims, demands, loss, costs, penalties.");
        terms.add("Insolvency: In the event of admission by National Companies Law Tribunal or any appropriate authority in this regard, of any application by Supplier to initiate corporate insolvency resolution process, the Buyer may terminate the PO in whole or in part and/or pursue any other remedies available legally.");
        terms.add("Survival: Any obligations or duties that by their nature survive and which extend the expiration or termination of the PO shall survive the expiration or termination of the PO.");
        terms.add("The PO together with these general terms & conditions, and any attachments, exhibits, specifications, drawings, notes and instructions and other information, whether physically attached or incorporated by reference collectively shall form part of the PO.");
        terms.add("Dispute Resolution through Arbitration: Any dispute(s)/difference(s) related to or arising out of this PO that cannot be settled amicably between the Buyer and Supplier within 30 (thirty) days of raising such dispute or difference, the aggrieved party can refer to sole arbitrator to be appointed mutually. The language and venue of arbitration will be Goa, India and the arbitrator will be governed by the laws and rules of India. In the absence of any ruling of arbitrator on the costs, each party shall share its own costs of such arbitration proceedings, unless it is the subject matter or one of the subject matter thereof.");
        terms.add("Governing Laws: This PO shall in all respects be subject to and governed by the laws of Republic of India without application of the conflict of laws principles and subject to the provisions of Dispute Resolution provided above, the Parties agree to submit to the exclusive jurisdiction of the courts of Goa (India).");
        terms.add("Compliance with Applicable Laws: The Supplier shall fully familiarize themselves and comply with all applicable laws, regulations etc. relating to taxation. The Supplier shall be bound to give all information, returns, etc., required by Applicable Laws, as aforesaid and to pay all fees and charges in respect thereof.");
        terms.add("Amendment: No amendment, modification or addition to this Agreement shall be effective or binding on either of the Parties unless set forth in writing and executed by both the Parties.");
        terms.add("Conflict: These General Terms & Conditions shall prevail over any other terms in current and any conflict with them.");

        // Add terms to document
        int termNumber = 14; // Continue numbering from previous page (after 13 terms on first page)
        for (String term : terms) {
            Paragraph paragraph = new Paragraph(termNumber + ". " + term, normalFont);
            paragraph.setSpacingAfter(5);
            document.add(paragraph);
            termNumber++;
        }

        // Create a container table for signature box and stamp
        PdfPTable containerTable = new PdfPTable(2);
        containerTable.setWidthPercentage(100);
        containerTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        containerTable.setSpacingBefore(10);
        containerTable.setWidths(new float[]{3, 1}); // Signature box larger, stamp smaller

        // Add signature box with digital signature information only
        PdfPTable signatureTable = new PdfPTable(1);
        signatureTable.setWidthPercentage(100);

        // Header row
        PdfPCell headerCell = new PdfPCell(new Phrase("For Synergy Shipbuilders:\nPrepared By:"));
        headerCell.setBorderWidth(1);
        headerCell.setPadding(3);
        signatureTable.addCell(headerCell);

        // Empty signature cell for digital signing
        PdfPCell digitalSignCell = new PdfPCell(new Phrase(""));
        digitalSignCell.setBorderWidth(1);
        digitalSignCell.setPadding(5);
        digitalSignCell.setFixedHeight(40);
        digitalSignCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        digitalSignCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        signatureTable.addCell(digitalSignCell);

        // Add signature table to container
        PdfPCell signatureContainerCell = new PdfPCell(signatureTable);
        signatureContainerCell.setBorder(Rectangle.NO_BORDER);
        signatureContainerCell.setPadding(0);
        containerTable.addCell(signatureContainerCell);

        // Add stamp logo
        try {
            // Load stamp logo from resources
            URL stampUrl = getClass().getClassLoader().getResource("static/images/stamp-logo.jpeg");
            if (stampUrl != null) {
                Image stampImage = Image.getInstance(stampUrl);
                stampImage.scaleToFit(60, 60); // Scale to appropriate size

                PdfPCell stampCell = new PdfPCell(stampImage);
                stampCell.setBorder(Rectangle.NO_BORDER);
                stampCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                stampCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                stampCell.setPadding(5);
                containerTable.addCell(stampCell);
            } else {
                // Fallback if stamp image not found
                PdfPCell emptyStampCell = new PdfPCell(new Phrase(""));
                emptyStampCell.setBorder(Rectangle.NO_BORDER);
                containerTable.addCell(emptyStampCell);
            }
        } catch (Exception e) {
            // Fallback if stamp image loading fails
            PdfPCell emptyStampCell = new PdfPCell(new Phrase(""));
            emptyStampCell.setBorder(Rectangle.NO_BORDER);
            containerTable.addCell(emptyStampCell);
        }

        document.add(containerTable);
    }

    /**
     * Adds all terms and conditions on a single page using two-column layout and signature box at bottom
     */
    private void addTermsAndConditionsSinglePage(Document document) throws DocumentException {
        Font titleFont = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD);
        Font normalFont = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL); // Readable font size

        // Title
        Paragraph title = new Paragraph("Terms & Conditions", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(10);
        document.add(title);

        // All terms and conditions content - exact as provided
        List<String> allTerms = new ArrayList<>();

        allTerms.add("In this Purchase Order (PO), Buyer means Synergy Shipbuilders who has issued the PO and the 'Supplier' shall mean the person/company/organization from whom the Goods and/or Services are being bought and these terms and conditions form an integral part of the PO.");
        allTerms.add("Acceptance: This PO constitutes the Buyer's final offer to the Supplier and it shall become a binding and enforceable agreement. Acceptance from the supplier has to be conveyed to the buyer with 2-3 working days.");
        allTerms.add("Price: The prices mentioned in the PO are fixed and not subject to any escalation for any reasons whatsoever.");
        allTerms.add("Charges: All charges towards packing, forwarding, loading and transportation shall be deemed as included in the PO price unless otherwise mentioned in the PO. Any breakage, damage, or pilferage in transit shall be borne by the supplier. Unloading at buyer's site shall be arranged by the buyer.");
        allTerms.add("Insurance: Unless otherwise mentioned in the PO, the expenses on account of arrangement and provision of transit insurance of goods for 100% of PO value up to unloading of goods at the delivery destination specified in the PO shall be borne by the supplier.");
        allTerms.add("Export and Import procedures: The company is fully responsible to arrange all the export documents prior to shipping of the goods. Unless/ until confirmed by the buyer's team all paperwork is in place, the supplier is not allowed to release the goods.");
        allTerms.add("Delivery: The supplier agrees that timely delivery in accordance with the delivery schedule mentioned in the PO is the essence of the PO. Buyer further reserves the right to request to expediate the delivery of the goods if required. Any damages occurred in case of delays, the supplier is to be responsible and buyer holds the right to cancel the order.");
        allTerms.add("Liquidated damages for delay:");
        allTerms.add("Warranties & Guarantees: The supplier warranties and guarantees all goods as per the agreed terms and undertakes to provide all necessary Certificates & Catalogues as applicable. Any defects observed either in the quality of materials or workmanship will be the responsibility of the supplier during the warranty period.");
        allTerms.add("Inspection / Rejection of Goods / Services: Unless otherwise mentioned in the PO, all goods/services received are subject to final approval of the buyer and inspection regarding quality, quantity and specifications. Any defects noticed at the time of assembling or processing, even if the first instance of goods have been accepted and paid for, the buyer reserves the right to reject the goods owing to manufacturing defects at any time.");
        allTerms.add("The buyer reserves the right to terminate or amend the PO or any part thereof for the following reasons, without assigning any reason and without in any manner incurring any liability due irregularities in the supply, rejections, escalations in the prices, not required by the buyer, risk of purchase.");
        allTerms.add("The supplier holds the responsibility on informing the buyer of any irregularities in fulfilling the PO. Any excess items will be returned on the suppliers cost and any short items needs to be fulfilled on priority. The buyer reserves the right to withhold payment or terminate the PO. Any damages / delays occurred will be the responsibility of the supplier.");
        allTerms.add("Intellectual Property rights: The supplier shall ensure that the goods/services supplied are not infringing any and all Intellectual Property Rights including but not limited to patents, copyrights, designs and trademarks.");
        allTerms.add("Limitation of Buyers Liability: Under any situation, the buyer shall not be liable for any loss of profit, incidental, indirect and consequential damages irrespective f whether such claim is based on tort or otherwise. Buyers liability on any other kind of loss or damage arising out of or connected with this PO or the contract based on this or on the performance or otherwise thereon, shall in no case exceed a maximum of 5% of the price allocable to the goods/services thereof which give rise to such a claim and would be bound by the Law of Limitation.");
        allTerms.add("Confidentiality and Publicity: All drawings, designs, jogs, tools, patterns, and samples, if any provided by the buyer to the supplier in respect of the PO are to be treated as confidential and shall be used only for the purpose of performance of the PO and shall be an exclusive property of the buyer. Under any circumstances the supplier shall not reproduce or disclose in whole or part or any other purpose to third party(ies) without prior consent in writing from buyer. Both the parties are bound by this confidentiality clause, as implied by law.");
        allTerms.add("Indemnity: The supplier shall indemnify, defend and hold harmless the buyer or affiliates to any claims, demands, loss, costs, penalties.");
        allTerms.add("Insolvency: In the event of admission by National Companies Law Tribunal or any appropriate authority in this regard, of any application against Supplier to initiate corporate insolvency resolution process, the Buyer may terminate the PO in whole or in part and/or pursue any other remedies available legally.");
        allTerms.add("Survival: Any obligations or duties that by their nature survive and which extend the expiration or termination of the PO shall survive the expiration or termination of the PO.");
        allTerms.add("The PO together with these General terms & conditions, and any attachments, exhibits, specifications, drawings, notes and instructions and other information, whether physically attached or incorporated by reference collectively shall form part of the PO.");
        allTerms.add("Dispute Resolution through Arbitration: 20.1 Any dispute(s)/difference(s) related to or arising out of this PO that cannot be settled amicably between the Buyer and Supplier within 30 (thirty) days of raising such dispute or difference will be referred to a sole arbitrator, to be appointed mutually. The juridical seat and venue of arbitration shall be Goa, India and the language of arbitration shall be English. The award of sole arbitrator will be final and binding on both the parties. In the absence of any ruling of arbitrator on the costs, each party shall share its own costs of such arbitration. The arbitration will be carried out in accordance with the provisions of Indian Arbitration and Conciliation Act, 1996 as applicable on the date of reference to arbitration. 20.2 Supply of Goods/Service under the PO will be continued by the Supplier during the arbitration proceedings, unless otherwise directed in writing by the Buyer or unless the matter is such that the supply of Goods/Services cannot possibly be continued until the decision of the arbitrator is obtained and save as those which are otherwise expressly provided in the PO no payment due or payable by the Buyer shall be withheld on account of such arbitration proceedings, unless it is the subject matter or one of the subject matter thereof.");
        allTerms.add("Governing Laws: This PO shall in all respects be subject to and governed by the laws of Republic of India without application of the conflict of laws principles and subject to the provisions of Dispute Resolution provided above, the Parties agree to submit to the exclusive jurisdiction of the courts of Goa (India).");
        allTerms.add("Compliance with Applicable Laws: The Supplier shall fully familiarize itself and conform in all aspects with all Applicable Laws including laws, regulations etc. relating to taxation. The Supplier shall be bound to give all notices, file all returns, etc., required by Applicable Laws, as aforesaid and to pay all fees and charges in respect thereof.");
        allTerms.add("Amendment: No amendment, modification or addition to this Agreement shall be effective or binding on either of the Parties unless set forth in writing and executed by both the Parties.");
        allTerms.add("Conflict: These General Terms & Conditions shall prevail over any other terms in case of any conflict with them.");

        // Create a two-column table for terms and conditions
        PdfPTable termsTable = new PdfPTable(2);
        termsTable.setWidthPercentage(100);
        termsTable.setSpacingBefore(5);
        termsTable.setWidths(new float[]{1, 1}); // Equal width columns

        // Split terms: first 13 in left column, remaining in right column
        int totalTerms = allTerms.size();
        int leftColumnCount = 13; // First 13 terms in left column

        // Left column terms (first 13)
        StringBuilder leftColumnText = new StringBuilder();
        for (int i = 0; i < leftColumnCount && i < totalTerms; i++) {
            leftColumnText.append((i + 1)).append(". ").append(allTerms.get(i)).append("\n\n");
        }

        // Right column terms (remaining terms from 14 onwards)
        StringBuilder rightColumnText = new StringBuilder();
        for (int i = leftColumnCount; i < totalTerms; i++) {
            rightColumnText.append((i + 1)).append(". ").append(allTerms.get(i)).append("\n\n");
        }

        // Add left column
        PdfPCell leftCell = new PdfPCell(new Phrase(leftColumnText.toString(), normalFont));
        leftCell.setBorder(Rectangle.NO_BORDER);
        leftCell.setPadding(5);
        leftCell.setVerticalAlignment(Element.ALIGN_TOP);
        termsTable.addCell(leftCell);

        // Add right column
        PdfPCell rightCell = new PdfPCell(new Phrase(rightColumnText.toString(), normalFont));
        rightCell.setBorder(Rectangle.NO_BORDER);
        rightCell.setPadding(5);
        rightCell.setVerticalAlignment(Element.ALIGN_TOP);
        termsTable.addCell(rightCell);

        document.add(termsTable);

        // Add some space before signature section
        Paragraph spacer = new Paragraph(" ", normalFont);
        spacer.setSpacingAfter(10);
        document.add(spacer);

        // Create a container table for signature box and stamp
        PdfPTable containerTable = new PdfPTable(2);
        containerTable.setWidthPercentage(100);
        containerTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        containerTable.setSpacingBefore(10);
        containerTable.setWidths(new float[]{3, 1}); // Signature box larger, stamp smaller

        // Add signature box with digital signature information only
        PdfPTable signatureTable = new PdfPTable(1);
        signatureTable.setWidthPercentage(100);

        // Header row
        PdfPCell headerCell = new PdfPCell(new Phrase("For Synergy Shipbuilders:\nPrepared By:", normalFont));
        headerCell.setBorderWidth(1);
        headerCell.setPadding(3);
        signatureTable.addCell(headerCell);

        // Empty signature cell for digital signing
        PdfPCell digitalSignCell = new PdfPCell(new Phrase(""));
        digitalSignCell.setBorderWidth(1);
        digitalSignCell.setPadding(5);
        digitalSignCell.setFixedHeight(40);
        digitalSignCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        digitalSignCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        signatureTable.addCell(digitalSignCell);

        // Add signature table to container
        PdfPCell signatureContainerCell = new PdfPCell(signatureTable);
        signatureContainerCell.setBorder(Rectangle.NO_BORDER);
        signatureContainerCell.setPadding(0);
        containerTable.addCell(signatureContainerCell);

        // Add stamp logo
        try {
            // Load stamp logo from resources
            URL stampUrl = getClass().getClassLoader().getResource("static/images/stamp-logo.jpeg");
            if (stampUrl != null) {
                Image stampImage = Image.getInstance(stampUrl);
                stampImage.scaleToFit(60, 60); // Scale to appropriate size

                PdfPCell stampCell = new PdfPCell(stampImage);
                stampCell.setBorder(Rectangle.NO_BORDER);
                stampCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                stampCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                stampCell.setPadding(5);
                containerTable.addCell(stampCell);
            } else {
                // Fallback if stamp image not found
                PdfPCell emptyStampCell = new PdfPCell(new Phrase(""));
                emptyStampCell.setBorder(Rectangle.NO_BORDER);
                containerTable.addCell(emptyStampCell);
            }
        } catch (Exception e) {
            // Fallback if stamp image loading fails
            PdfPCell emptyStampCell = new PdfPCell(new Phrase(""));
            emptyStampCell.setBorder(Rectangle.NO_BORDER);
            containerTable.addCell(emptyStampCell);
        }

        document.add(containerTable);
    }



    /**
     * Generates a Purchase Order PDF using letterhead template with existing content
     */
    public void generateVendorPurchaseOrderPdf(VendorSplitPODTO vendorPO,
                                             PurchaseOrderDeliveryTermsDTO deliveryTerms,
                                             HttpServletResponse response) throws DocumentException, IOException {

        // First create the PDF content as before
        ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
        com.itextpdf.text.Document document = new com.itextpdf.text.Document(PageSize.A4);
        PdfWriter writer = PdfWriter.getInstance(document, tempOutputStream);
        document.open();

        // Add only line items content for the plain template (no header/footer content)
        addPlainTemplateLineItems(document, vendorPO);

        // Add terms and conditions page with addresses only
        document.newPage();
        addTCPageAddresses(document, deliveryTerms);

        document.close();

        // Now overlay this content on the appropriate templates
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_order_" + vendorPO.getVendorPoId() + ".pdf");

        // Load the two separate templates
        InputStream lineItemsTemplateStream = getClass().getResourceAsStream("/static/images/PO FORMAT REFINED plain.pdf");
        InputStream tcTemplateStream = getClass().getResourceAsStream("/static/images/PO FORMAT REFINED t&c page.pdf");

        if (lineItemsTemplateStream == null) {
            // If templates not found, just return the original PDF
            response.getOutputStream().write(tempOutputStream.toByteArray());
            return;
        }

        // Create final PDF with templates as background
        PdfReader lineItemsTemplateReader = new PdfReader(lineItemsTemplateStream);
        PdfReader tcTemplateReader = null;
        if (tcTemplateStream != null) {
            tcTemplateReader = new PdfReader(tcTemplateStream);
        }

        PdfReader contentReader = new PdfReader(tempOutputStream.toByteArray());
        PdfStamper stamper = new PdfStamper(contentReader, response.getOutputStream());

        // Get total number of pages in the content
        int totalPages = contentReader.getNumberOfPages();

        // Apply templates to each page
        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            PdfContentByte canvas = stamper.getUnderContent(pageNum);

            if (pageNum < totalPages) {
                // For all pages except the last (T&C page): Use line items template
                PdfImportedPage lineItemsPage = stamper.getImportedPage(lineItemsTemplateReader, 1);
                canvas.addTemplate(lineItemsPage, 0, 0);
            } else {
                // Last page (T&C page): Use T&C template
                if (tcTemplateReader != null) {
                    PdfImportedPage tcPage = stamper.getImportedPage(tcTemplateReader, 1);
                    canvas.addTemplate(tcPage, 0, 0);
                }
                // If T&C template not found, page remains as is
            }
        }

        stamper.close();
        contentReader.close();
        lineItemsTemplateReader.close();
        if (tcTemplateReader != null) {
            tcTemplateReader.close();
        }
        lineItemsTemplateStream.close();
        if (tcTemplateStream != null) {
            tcTemplateStream.close();
        }
    }
    /**
     * Generates a Purchase Order Word document using Word template with data overlay
     */
    public void generateVendorPurchaseOrderDocx(VendorSplitPODTO vendorPO,
                                               PurchaseOrderDeliveryTermsDTO deliveryTerms,
                                               HttpServletResponse response) throws IOException {
        try {
            // Load the main template
            InputStream templateStream = getClass().getResourceAsStream("/static/images/PO FORMAT REFINED plain.docx");
            if (templateStream == null) {
                throw new IOException("Word template not found: PO FORMAT REFINED plain.docx");
            }

            // Create document from template
            XWPFDocument document = new XWPFDocument(OPCPackage.open(templateStream));

            // Populate template with vendor PO data
            populateWordTemplate(document, vendorPO, deliveryTerms);

            // Handle line items with pagination (10 items per page)
            addLineItemsToWordDocument(document, vendorPO);

            // Add T&C page from second template
            addTermsAndConditionsPage(document);

            // Set response headers for Word document download
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=purchase_order_" + vendorPO.getVendorPoId() + ".docx");

            // Write document to response
            document.write(response.getOutputStream());
            document.close();
            templateStream.close();

        } catch (Exception e) {
            throw new IOException("Error generating Word document: " + e.getMessage(), e);
        }
    }

    /**
     * Populates the Word template with vendor PO data
     */
    private void populateWordTemplate(XWPFDocument document, VendorSplitPODTO vendorPO, PurchaseOrderDeliveryTermsDTO deliveryTerms) {
        // Replace placeholders in paragraphs
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceTextInParagraph(paragraph, vendorPO, deliveryTerms);
        }

        // Replace placeholders in tables
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceTextInParagraph(paragraph, vendorPO, deliveryTerms);
                    }
                }
            }
        }

        // Replace placeholders in headers and footers
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceTextInParagraph(paragraph, vendorPO, deliveryTerms);
            }
        }
    }

    /**
     * Replaces placeholder text in a paragraph with actual data
     */
    private void replaceTextInParagraph(XWPFParagraph paragraph, VendorSplitPODTO vendorPO, PurchaseOrderDeliveryTermsDTO deliveryTerms) {
        String text = paragraph.getText();
        if (text != null) {
            // Corporate Office (hardcoded as per template)
            text = text.replace("{{CORPORATE_OFFICE}}", "Epic center by Wadhwa Building, Office no. 1012, 10th Floor, W T Patil Marg, Chembur, Mumbai - 400071");

            // Vendor Details
            text = text.replace("{{VENDOR_NAME}}", vendorPO.getVendorCompanyName() != null ? vendorPO.getVendorCompanyName() : "");
            text = text.replace("{{VENDOR_EMAIL}}", vendorPO.getVendorEmail() != null ? vendorPO.getVendorEmail() : "");
            text = text.replace("{{VENDOR_ADDRESS}}", vendorPO.getVendorAddress() != null ? vendorPO.getVendorAddress() : "");

            // PO Details
            text = text.replace("{{PROJECT_NAME}}", vendorPO.getProjectName() != null ? vendorPO.getProjectName() : "");
            text = text.replace("{{PO_NUMBER}}", vendorPO.getVendorPoId() != null ? vendorPO.getVendorPoId() : "");
            text = text.replace("{{PO_DATE}}", vendorPO.getCreatedDate() != null ? dateFormat.format(vendorPO.getCreatedDate()) : "");
            text = text.replace("{{PAYMENT_TERMS}}", deliveryTerms.getPaymentTerms() != null ? deliveryTerms.getPaymentTerms() : "");
            text = text.replace("{{DELIVERY_TERMS}}", deliveryTerms.getDeliveryTerms() != null ? deliveryTerms.getDeliveryTerms() : "");

            // Top Right Details
            text = text.replace("{{YARD_NUMBER}}", vendorPO.getYardNumber() != null ? vendorPO.getYardNumber() : "");
            text = text.replace("{{CURRENCY}}", vendorPO.getCurrency() != null ? vendorPO.getCurrency() : "");
            text = text.replace("{{TOTAL_AMOUNT}}", vendorPO.getVendorGrandTotal() != null ? vendorPO.getVendorGrandTotal().toString() : "");
            text = text.replace("{{DELIVERY_DATE}}", deliveryTerms.getDeliveryDate() != null ? dateFormat.format(deliveryTerms.getDeliveryDate()) : "");

            // Clear existing runs and add new text
            if (!text.equals(paragraph.getText())) {
                // Remove all existing runs
                for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }
                // Add new run with replaced text
                XWPFRun run = paragraph.createRun();
                run.setText(text);
            }
        }
    }
    /**
     * Adds line items to the Word document with pagination
     */
    private void addLineItemsToWordDocument(XWPFDocument document, VendorSplitPODTO vendorPO) {
        List<ApprovedPOLineItemDTO> lineItems = vendorPO.getLineItems();
        if (lineItems == null || lineItems.isEmpty()) {
            return;
        }

        // Find the line items table in the template (assuming it's the last table)
        XWPFTable lineItemsTable = null;
        for (XWPFTable table : document.getTables()) {
            // Look for table with "Sr.No" header to identify line items table
            if (table.getRows().size() > 0) {
                XWPFTableRow headerRow = table.getRow(0);
                if (headerRow.getTableCells().size() > 0) {
                    String firstCellText = headerRow.getCell(0).getText();
                    if (firstCellText.contains("Sr.No") || firstCellText.contains("Sr No")) {
                        lineItemsTable = table;
                        break;
                    }
                }
            }
        }

        if (lineItemsTable != null) {
            // Remove existing data rows (keep header)
            while (lineItemsTable.getRows().size() > 1) {
                lineItemsTable.removeRow(1);
            }

            // Add line items (max 10 per page)
            int itemsPerPage = 10;
            for (int i = 0; i < lineItems.size(); i++) {
                ApprovedPOLineItemDTO item = lineItems.get(i);

                // Create new page if needed (after every 10 items)
                if (i > 0 && i % itemsPerPage == 0) {
                    // Add page break and duplicate table structure for next page
                    XWPFParagraph pageBreak = document.createParagraph();
                    pageBreak.setPageBreak(true);

                    // Create new table for next page
                    lineItemsTable = document.createTable();
                    // Copy header row structure from original table
                    // This is a simplified approach - in production you might want to copy exact formatting
                }

                // Add row for this item
                XWPFTableRow row = lineItemsTable.createRow();

                // Ensure we have enough cells
                while (row.getTableCells().size() < 7) {
                    row.createCell();
                }

                // Populate cells: Sr.No | Items Description | Dimensions/Specifications | UOM | Qty | Unit Price | Amount
                row.getCell(0).setText(String.valueOf(i + 1)); // Sr.No
                row.getCell(1).setText(item.getDescription() != null ? item.getDescription() : ""); // Items Description
                row.getCell(2).setText(""); // Dimensions/Specifications (not in current data model)
                row.getCell(3).setText(item.getUnitOfMeasure() != null ? item.getUnitOfMeasure() : ""); // UOM
                row.getCell(4).setText(item.getQuantity() != null ? item.getQuantity().toString() : ""); // Qty
                row.getCell(5).setText(item.getRateApproved() != null ? item.getRateApproved().toString() : ""); // Unit Price
                row.getCell(6).setText(item.getTotal() != null ? item.getTotal().toString() : ""); // Amount
            }
        }
    }

    /**
     * Adds Terms & Conditions page from second template
     */
    private void addTermsAndConditionsPage(XWPFDocument document) {
        try {
            InputStream tcTemplateStream = getClass().getResourceAsStream("/static/images/PO FORMAT REFINED t&c page.docx");
            if (tcTemplateStream != null) {
                XWPFDocument tcDocument = new XWPFDocument(OPCPackage.open(tcTemplateStream));

                // Add page break before T&C
                XWPFParagraph pageBreak = document.createParagraph();
                pageBreak.setPageBreak(true);

                // Copy all content from T&C template
                for (XWPFParagraph paragraph : tcDocument.getParagraphs()) {
                    XWPFParagraph newParagraph = document.createParagraph();
                    // Copy paragraph content and formatting
                    for (XWPFRun run : paragraph.getRuns()) {
                        XWPFRun newRun = newParagraph.createRun();
                        newRun.setText(run.getText(0));
                        // Copy basic formatting
                        newRun.setBold(run.isBold());
                        newRun.setItalic(run.isItalic());
                        if (run.getFontSize() > 0) {
                            newRun.setFontSize(run.getFontSize());
                        }
                    }
                }

                // Copy tables from T&C template
                for (XWPFTable table : tcDocument.getTables()) {
                    XWPFTable newTable = document.createTable();
                    // Copy table structure and content
                    for (int i = 0; i < table.getRows().size(); i++) {
                        XWPFTableRow sourceRow = table.getRow(i);
                        XWPFTableRow newRow = (i == 0) ? newTable.getRow(0) : newTable.createRow();

                        for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                            XWPFTableCell sourceCell = sourceRow.getCell(j);
                            XWPFTableCell newCell = (j < newRow.getTableCells().size()) ?
                                newRow.getCell(j) : newRow.createCell();
                            newCell.setText(sourceCell.getText());
                        }
                    }
                }

                tcDocument.close();
                tcTemplateStream.close();
            }
        } catch (Exception e) {
            System.out.println("Warning: Could not add T&C page: " + e.getMessage());
        }
    }





    /**
     * Adds the order information table for vendor PO - EXACT COPY with vendor data
     */
    private void addVendorOrderInfoTable(Document document, VendorSplitPODTO vendorPO) throws DocumentException {
        PdfPTable orderTable = new PdfPTable(1);
        orderTable.setWidthPercentage(100);

        // ORDER header - extended as a column header
        PdfPCell orderHeaderCell = new PdfPCell();
        orderHeaderCell.setBorderWidth(1);
        orderHeaderCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        orderHeaderCell.setBackgroundColor(BaseColor.BLACK);
        Font orderHeaderFont = new Font(Font.FontFamily.HELVETICA, 12, Font.BOLD, BaseColor.WHITE);
        orderHeaderCell.setPhrase(new Phrase("ORDER", orderHeaderFont));
        orderTable.addCell(orderHeaderCell);

        // Get current financial year in YY-YY format
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1; // Calendar months are 0-based
        String financialYear;
        if (month >= 4) { // April onwards is new financial year in India
            // Format as YY-YY (e.g., 25-26 instead of 2025-2026)
            financialYear = String.format("%02d-%02d", year % 100, (year + 1) % 100);
        } else {
            financialYear = String.format("%02d-%02d", (year - 1) % 100, year % 100);
        }

        // Create a table for PO details with 2 columns
        PdfPTable poDetailsTable = new PdfPTable(2);
        poDetailsTable.setWidthPercentage(100);
        poDetailsTable.setWidths(new float[]{1, 1});

        // Purchase Order #
        PdfPCell poLabelCell = new PdfPCell(new Phrase("Purchase Order #"));
        poLabelCell.setBorderWidth(1);
        poDetailsTable.addCell(poLabelCell);

        // Date Created
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        PdfPCell dateCreatedCell = new PdfPCell(new Phrase("Date Created"));
        dateCreatedCell.setBorderWidth(1);
        poDetailsTable.addCell(dateCreatedCell);

        // Generate order number in format: SS/[Vendor Initials]/[Vendor PO ID]/[Financial Year]
        String vendorInitials = "";
        String vendorName = vendorPO.getVendorName() != null ? vendorPO.getVendorName() :
                          (vendorPO.getVendorCompanyName() != null ? vendorPO.getVendorCompanyName() : "");

        if (!vendorName.isEmpty()) {
            // Extract initials (first letter of each word)
            String[] words = vendorName.split("\\s+");
            StringBuilder initialsBuilder = new StringBuilder();
            for (String word : words) {
                if (!word.isEmpty()) {
                    initialsBuilder.append(word.charAt(0));
                }
            }
            vendorInitials = initialsBuilder.toString().toUpperCase();
        }

        // If no vendor initials, use "XX" as fallback
        if (vendorInitials.isEmpty()) {
            vendorInitials = "XX";
        }

        // Format the order number using the vendor PO ID (e.g., 647-1)
        String orderNumber = "SS/" + vendorInitials + "/" + vendorPO.getVendorPoId() + "/" + financialYear;

        // Order number value
        PdfPCell poValueCell = new PdfPCell(new Phrase(orderNumber));
        poValueCell.setBorderWidth(1);
        poDetailsTable.addCell(poValueCell);

        // Date value - use current date
        PdfPCell dateValueCell = new PdfPCell(new Phrase(dateFormat.format(new java.util.Date())));
        dateValueCell.setBorderWidth(1);
        poDetailsTable.addCell(dateValueCell);

        // Note about quoting order number - add to inner table spanning both columns
        PdfPCell noteCell = new PdfPCell(new Phrase("Please quote the order no. for all future correspondents and invoices"));
        noteCell.setBorderWidth(1);
        noteCell.setColspan(2); // Span across both columns of the inner table
        poDetailsTable.addCell(noteCell);

        // Add the PO details table to the main order table
        PdfPCell poDetailsCell = new PdfPCell();
        poDetailsCell.setBorderWidth(0);
        poDetailsCell.addElement(poDetailsTable);
        orderTable.addCell(poDetailsCell);

        document.add(orderTable);
    }

    /**
     * Adds the addresses section for vendor PO - EXACT COPY with vendor data
     */
    private void addVendorAddressesSection(Document document, VendorSplitPODTO vendorPO,
                                         PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        PdfPTable addressTable = new PdfPTable(2);
        addressTable.setWidthPercentage(100);
        addressTable.setSpacingBefore(10);

        // Supplier details
        PdfPCell supplierHeaderCell = new PdfPCell(new Phrase("SUPPLIER DETAILS:"));
        supplierHeaderCell.setBorderWidth(1);
        supplierHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(supplierHeaderCell);

        // Invoice address
        PdfPCell invoiceHeaderCell = new PdfPCell(new Phrase("INVOICE ADDRESS - Bill To:"));
        invoiceHeaderCell.setBorderWidth(1);
        invoiceHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(invoiceHeaderCell);

        // Supplier details content - vendor details
        String supplierDetails = vendorPO.getVendorCompanyName();

        // Add vendor contact details
        if (vendorPO.getVendorContactNumber() != null) {
            supplierDetails += "\nPhone: " + vendorPO.getVendorContactNumber();
        }

        if (vendorPO.getVendorEmail() != null) {
            supplierDetails += "\nEmail: " + vendorPO.getVendorEmail();
        }

        if (vendorPO.getVendorAddress() != null) {
            String formattedAddress = formatVendorAddress(vendorPO.getVendorAddress());
            if (formattedAddress != null && !formattedAddress.trim().isEmpty()) {
                supplierDetails += "\n" + formattedAddress;
            }
        }

        PdfPCell supplierDetailsCell = new PdfPCell(new Phrase(supplierDetails));
        supplierDetailsCell.setBorderWidth(1);
        supplierDetailsCell.setMinimumHeight(60);
        addressTable.addCell(supplierDetailsCell);

        // Get invoice address from the form (with fallback to deliveryLocation for backward compatibility)
        String invoiceAddress = deliveryTerms.getInvoiceAddress();
        if (invoiceAddress == null || invoiceAddress.trim().isEmpty()) {
            invoiceAddress = deliveryTerms.getDeliveryLocation(); // Fallback to old field
            if (invoiceAddress == null || invoiceAddress.trim().isEmpty()) {
                invoiceAddress = "Synergy Shipbuilders Yard, Gauaudaulim,\nCumbarjua, Tiswadi, Ilhas – Goa, India";
            }
        }

        PdfPCell invoiceAddressCell = new PdfPCell(new Phrase(invoiceAddress));
        invoiceAddressCell.setBorderWidth(1);
        invoiceAddressCell.setMinimumHeight(60);
        addressTable.addCell(invoiceAddressCell);

        // Sold to
        PdfPCell soldToHeaderCell = new PdfPCell(new Phrase("SOLD TO:"));
        soldToHeaderCell.setBorderWidth(1);
        soldToHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(soldToHeaderCell);

        // Delivery address
        PdfPCell deliveryHeaderCell = new PdfPCell(new Phrase("DELIVERY ADDRESS - Ship To:"));
        deliveryHeaderCell.setBorderWidth(1);
        deliveryHeaderCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        addressTable.addCell(deliveryHeaderCell);

        // Sold to content - from frontend selection with fallback
        String soldToAddress = deliveryTerms.getSoldToAddress();
        if (soldToAddress == null || soldToAddress.trim().isEmpty()) {
            soldToAddress = "Synergy Shipbuilders\n" +
                           "Yard: Gauaudaulim, Cumbarjua, Tiswadi, Ilhas\n" +
                           "Goa – India\n" +
                           "GST: 30ADKFS3803J1Z7";
        }

        PdfPCell soldToCell = new PdfPCell(new Phrase(soldToAddress));
        soldToCell.setBorderWidth(1);
        soldToCell.setMinimumHeight(60);
        addressTable.addCell(soldToCell);

        // Get delivery address from the form (with fallback to deliveryLocation for backward compatibility)
        String deliveryAddress = deliveryTerms.getDeliveryAddress();
        if (deliveryAddress == null || deliveryAddress.trim().isEmpty()) {
            deliveryAddress = deliveryTerms.getDeliveryLocation(); // Fallback to old field
            if (deliveryAddress == null || deliveryAddress.trim().isEmpty()) {
                deliveryAddress = "Synergy Shipbuilders Yard, Gauaudaulim,\nCumbarjua, Tiswadi, Ilhas – Goa, India";
            }
        }

        PdfPCell deliveryAddressCell = new PdfPCell(new Phrase(deliveryAddress));
        deliveryAddressCell.setBorderWidth(1);
        deliveryAddressCell.setMinimumHeight(60);
        addressTable.addCell(deliveryAddressCell);

        document.add(addressTable);
    }

    /**
     * Adds the items table for vendor PO with pagination - splits into chunks of 15 items per page
     */
    private void addVendorItemsTable(Document document, VendorSplitPODTO vendorPO,
                                   PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        // Debug log
        System.out.println("DEBUG: Vendor PO Items:");
        if (vendorPO.getLineItems() != null) {
            for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
                System.out.println("Item: " + item.getDescription() +
                                  ", Qty: " + item.getQuantity() +
                                  ", Rate: " + item.getRateApproved() +
                                  ", Total: " + item.getTotal());
            }
        }

        List<ApprovedPOLineItemDTO> allItems = vendorPO.getLineItems();
        if (allItems == null || allItems.isEmpty()) {
            return;
        }

        // Calculate total amount for all items (needed for final totals)
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (ApprovedPOLineItemDTO item : allItems) {
            BigDecimal itemTotal = item.getTotal() != null ? item.getTotal() : BigDecimal.ZERO;
            totalAmount = totalAmount.add(itemTotal);
        }

        // Split items into chunks of 15 items per page
        final int ITEMS_PER_PAGE = 15;
        int totalItems = allItems.size();
        int totalPages = (int) Math.ceil((double) totalItems / ITEMS_PER_PAGE);

        System.out.println("DEBUG: Total items: " + totalItems + ", Pages needed: " + totalPages);

        int globalSrNo = 1;

        for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
            int startIndex = pageIndex * ITEMS_PER_PAGE;
            int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
            List<ApprovedPOLineItemDTO> pageItems = allItems.subList(startIndex, endIndex);

            System.out.println("DEBUG: Page " + (pageIndex + 1) + " - Items " + startIndex + " to " + (endIndex - 1));

            // Create table for this page
            PdfPTable itemsTable = new PdfPTable(5);
            itemsTable.setWidthPercentage(100);
            itemsTable.setSpacingBefore(10);
            itemsTable.setWidths(new float[]{0.5f, 3, 1, 1, 1.5f});

            // Add table headers
            Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
            String[] headers = {"Sr. No", "Description", "Qty", "Unit Rate", "Total amount"};
            for (String header : headers) {
                PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
                cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                cell.setPadding(5);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                itemsTable.addCell(cell);
            }

            // Add data rows for this page
            Font dataFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);

            for (ApprovedPOLineItemDTO item : pageItems) {
                // Sr. No (continues from previous pages)
                PdfPCell srNoCell = new PdfPCell(new Phrase(String.valueOf(globalSrNo++), dataFont));
                srNoCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                itemsTable.addCell(srNoCell);

                // Description
                PdfPCell descriptionCell = new PdfPCell(new Phrase(item.getDescription(), dataFont));
                itemsTable.addCell(descriptionCell);

                // Quantity
                PdfPCell qtyCell = new PdfPCell(new Phrase(String.valueOf(item.getQuantity()), dataFont));
                qtyCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                itemsTable.addCell(qtyCell);

                // Unit Rate
                BigDecimal rate = item.getRateApproved() != null ? item.getRateApproved() : BigDecimal.ZERO;
                rate = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                PdfPCell rateCell = new PdfPCell(new Phrase(rate.toString(), dataFont));
                rateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                itemsTable.addCell(rateCell);

                // Total amount
                BigDecimal itemTotal = item.getTotal() != null ? item.getTotal() : BigDecimal.ZERO;
                itemTotal = itemTotal.setScale(2, BigDecimal.ROUND_HALF_UP);
                PdfPCell totalCell = new PdfPCell(new Phrase(itemTotal.toString(), dataFont));
                totalCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                itemsTable.addCell(totalCell);
            }

            // Add the table to document
            document.add(itemsTable);

            // Add totals only on the last page
            boolean isLastPage = (pageIndex == totalPages - 1);
            if (isLastPage) {
                addTotalsToTable(document, vendorPO, deliveryTerms, totalAmount, headerFont);
            }

            // Add page break if not the last page
            if (!isLastPage) {
                document.newPage();
            }
        }
    }


    // Helper methods for excel export
    private String nullValue(String value) {
        return value == null ? "" : value;
    }

    private double getDouble(Float value) {
        return value == null ? 0.0 : value;
    }

    private String numberToString(Number value) {
        return value != null ? String.format("%.2f", value.doubleValue()) : "";
    }


    /**
     * Helper method to add totals section to the document
     */
    private void addTotalsToTable(Document document, VendorSplitPODTO vendorPO,
                                PurchaseOrderDeliveryTermsDTO deliveryTerms,
                                BigDecimal totalAmount, Font headerFont) throws DocumentException {

        // Create a separate table for totals to maintain consistent formatting
        PdfPTable totalsTable = new PdfPTable(5);
        totalsTable.setWidthPercentage(100);
        totalsTable.setSpacingBefore(5);
        totalsTable.setWidths(new float[]{0.5f, 3, 1, 1, 1.5f});

        // Format total amount to 2 decimal places
        totalAmount = totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);

        // Add total amount row
        PdfPCell totalLabelCell = new PdfPCell(new Phrase("TOTAL AMOUNT", headerFont));
        totalLabelCell.setColspan(4);
        totalLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(totalLabelCell);

        PdfPCell totalValueCell = new PdfPCell(new Phrase(totalAmount.toString(), headerFont));
        totalValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(totalValueCell);

        // Check if there's a revised grand total that's different from calculated total
        BigDecimal calculatedGrandTotal = totalAmount; // Start with total amount for calculation
        boolean hasRevision = vendorPO.getVendorRevisedGrandTotal() != null;
        BigDecimal amountForTaxCalculation = totalAmount; // Default to total amount

        // If there's a revision, show the revised amount and use it for tax calculation
        if (hasRevision) {
            BigDecimal revisedAmount = vendorPO.getVendorRevisedGrandTotal().setScale(2, BigDecimal.ROUND_HALF_UP);

            // Add revised amount row
            PdfPCell revisedAmountLabelCell = new PdfPCell(new Phrase("REVISED AMOUNT", headerFont));
            revisedAmountLabelCell.setColspan(4);
            revisedAmountLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            totalsTable.addCell(revisedAmountLabelCell);

            PdfPCell revisedAmountValueCell = new PdfPCell(new Phrase(revisedAmount.toString(), headerFont));
            revisedAmountValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            totalsTable.addCell(revisedAmountValueCell);

            // Use revised amount for tax calculation
            amountForTaxCalculation = revisedAmount;
        }

        // Add tax row (calculated on revised amount if present, otherwise on total amount)
        BigDecimal taxPercentage;
        try {
            taxPercentage = new BigDecimal(deliveryTerms.getTaxPercentage());
        } catch (NumberFormatException e) {
            // Default to 0 if tax percentage is not a valid number
            taxPercentage = BigDecimal.ZERO;
        }

        BigDecimal taxAmount;
        try {
            taxAmount = amountForTaxCalculation.multiply(taxPercentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
        } catch (ArithmeticException e) {
            // Handle division by zero or other arithmetic errors
            taxAmount = BigDecimal.ZERO;
        }

        // Format tax amount to 2 decimal places
        taxAmount = taxAmount.setScale(2, BigDecimal.ROUND_HALF_UP);

        // Debug log for tax calculation
        System.out.println("DEBUG: Tax calculation: " + amountForTaxCalculation + " * " + taxPercentage + "% = " + taxAmount);

        PdfPCell taxLabelCell = new PdfPCell(new Phrase("TAX " + deliveryTerms.getTaxPercentage() + "%", headerFont));
        taxLabelCell.setColspan(4);
        taxLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(taxLabelCell);

        PdfPCell taxValueCell = new PdfPCell(new Phrase(taxAmount.toString(), headerFont));
        taxValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(taxValueCell);

        // Calculate final grand total (revised amount + tax if revised, otherwise total amount + tax)
        BigDecimal finalGrandTotal = amountForTaxCalculation.add(taxAmount);
        finalGrandTotal = finalGrandTotal.setScale(2, BigDecimal.ROUND_HALF_UP);

        PdfPCell grandTotalLabelCell = new PdfPCell(new Phrase("GRAND TOTAL AMOUNT", headerFont));
        grandTotalLabelCell.setColspan(4);
        grandTotalLabelCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(grandTotalLabelCell);

        PdfPCell grandTotalValueCell = new PdfPCell(new Phrase(finalGrandTotal.toString(), headerFont));
        grandTotalValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        totalsTable.addCell(grandTotalValueCell);

        document.add(totalsTable);
    }

    /**
     * Helper method to format vendor address from JSON string to readable format
     */
    private String formatVendorAddress(String addressJson) {
        if (addressJson == null || addressJson.trim().isEmpty()) {
            return null;
        }

        try {
            // Parse JSON array of addresses
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.JsonNode addressArray = objectMapper.readTree(addressJson);

            if (addressArray.isArray() && addressArray.size() > 0) {
                // Get the first address from the array
                com.fasterxml.jackson.databind.JsonNode firstAddress = addressArray.get(0);

                StringBuilder formattedAddress = new StringBuilder();

                // Add address components in a readable format
                if (firstAddress.has("address") && !firstAddress.get("address").isNull()) {
                    String address = firstAddress.get("address").asText();
                    if (!address.trim().isEmpty()) {
                        formattedAddress.append(address);
                    }
                }

                if (firstAddress.has("city") && !firstAddress.get("city").isNull()) {
                    String city = firstAddress.get("city").asText();
                    if (!city.trim().isEmpty()) {
                        if (formattedAddress.length() > 0) {
                            formattedAddress.append(", ");
                        }
                        formattedAddress.append(city);
                    }
                }

                if (firstAddress.has("state") && !firstAddress.get("state").isNull()) {
                    String state = firstAddress.get("state").asText();
                    if (!state.trim().isEmpty()) {
                        if (formattedAddress.length() > 0) {
                            formattedAddress.append(", ");
                        }
                        formattedAddress.append(state);
                    }
                }

                if (firstAddress.has("country") && !firstAddress.get("country").isNull()) {
                    String country = firstAddress.get("country").asText();
                    if (!country.trim().isEmpty()) {
                        if (formattedAddress.length() > 0) {
                            formattedAddress.append(", ");
                        }
                        formattedAddress.append(country);
                    }
                }

                return formattedAddress.toString();
            }
        } catch (Exception e) {
            // If JSON parsing fails, return the original string (fallback)
            System.out.println("Failed to parse vendor address JSON: " + e.getMessage());
            return addressJson;
        }

        return null;
    }

    public void generateMRLineItemsTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=material_requisition_line_items_template.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Line Items Template");

        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setWrapText(true);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(40);

        String[] headers = {
                "Item Code Gemba suggested\n(Enter the Unique code for Material)",
                "Product Name\n(Enter Product name )",
                "Order Qty\n(Enter Qty to be ordered)."
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        Row precautionRow1 = sheet.createRow(1);
        Cell precautionCell1 = precautionRow1.createCell(5);
        precautionCell1.setCellValue("Precautions:-");

        Row precautionRow2 = sheet.createRow(2);
        precautionRow2.createCell(5).setCellValue("Add Item code properly as per the item master");

        Row precautionRow3 = sheet.createRow(3);
        precautionRow3.createCell(5).setCellValue("Add Product Name Properly as per the item Master");

        // Autosize columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    public void generatePRLineItemsTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=purchase_request_line_items_template.xlsx");

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Line Items Template");

        // Create header row
        CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setWrapText(true);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(40);

        String[] headers = {
                "Item Code Gemba suggested\n(Enter the Unique code for Material)",
                "Product Name\n(Enter Product name )",
                "Order Qty\n(Enter Qty to be ordered)."
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        Row precautionRow1 = sheet.createRow(1);
        Cell precautionCell1 = precautionRow1.createCell(5);
        precautionCell1.setCellValue("Precautions:-");

        Row precautionRow2 = sheet.createRow(2);
        precautionRow2.createCell(5).setCellValue("Add Item code properly as per the item master");

        Row precautionRow3 = sheet.createRow(3);
        precautionRow3.createCell(5).setCellValue("Add Product Name Properly as per the item Master");
        // Autosize columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * Add only line items content for plain template (template has header/footer)
     */
    private void addPlainTemplateLineItems(com.itextpdf.text.Document document, VendorSplitPODTO vendorPO) throws DocumentException {
        // Add spacing to account for template header
        Paragraph spacer = new Paragraph(" ");
        spacer.setSpacingAfter(150); // Adjust based on template header height
        document.add(spacer);

        // Add only line items table - template handles everything else
        addSimpleLineItemsTable(document, vendorPO);
    }

    /**
     * Add simple line items table for plain template
     */
    private void addSimpleLineItemsTable(com.itextpdf.text.Document document, VendorSplitPODTO vendorPO) throws DocumentException {
        Font headerFont = new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD);
        Font dataFont = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL);

        // Create line items table matching template format
        PdfPTable itemsTable = new PdfPTable(7);
        itemsTable.setWidthPercentage(100);
        itemsTable.setWidths(new float[]{8, 25, 25, 8, 8, 12, 14}); // Sr.No | Items Description | Dimensions/Specifications | UOM | Qty | Unit Price | Amount

        // Add headers
        String[] headers = {"Sr.No", "Items Description", "Dimensions/Specifications", "UOM", "Qty", "Unit Price", "Amount"};
        for (String header : headers) {
            PdfPCell headerCell = new PdfPCell(new Phrase(header, headerFont));
            headerCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            headerCell.setBorder(Rectangle.BOX);
            headerCell.setPadding(5);
            headerCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(headerCell);
        }

        // Add line items
        int srNo = 1;
        for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
            // Sr.No
            PdfPCell srNoCell = new PdfPCell(new Phrase(String.valueOf(srNo++), dataFont));
            srNoCell.setBorder(Rectangle.BOX);
            srNoCell.setPadding(3);
            srNoCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(srNoCell);

            // Items Description
            PdfPCell descCell = new PdfPCell(new Phrase(item.getDescription() != null ? item.getDescription() : "", dataFont));
            descCell.setBorder(Rectangle.BOX);
            descCell.setPadding(3);
            itemsTable.addCell(descCell);

            // Dimensions/Specifications (using material family or unique code)
            String specifications = "";
            if (item.getMaterialFamily() != null) {
                specifications = item.getMaterialFamily();
            }
            if (item.getUniqueCode() != null) {
                specifications += (specifications.isEmpty() ? "" : " - ") + item.getUniqueCode();
            }
            PdfPCell specCell = new PdfPCell(new Phrase(specifications, dataFont));
            specCell.setBorder(Rectangle.BOX);
            specCell.setPadding(3);
            itemsTable.addCell(specCell);

            // UOM
            PdfPCell uomCell = new PdfPCell(new Phrase(item.getUnitOfMeasure() != null ? item.getUnitOfMeasure() : "", dataFont));
            uomCell.setBorder(Rectangle.BOX);
            uomCell.setPadding(3);
            uomCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            itemsTable.addCell(uomCell);

            // Qty
            PdfPCell qtyCell = new PdfPCell(new Phrase(item.getQuantity() != null ? item.getQuantity().toString() : "0", dataFont));
            qtyCell.setBorder(Rectangle.BOX);
            qtyCell.setPadding(3);
            qtyCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            itemsTable.addCell(qtyCell);

            // Unit Price (using rateApproved)
            PdfPCell priceCell = new PdfPCell(new Phrase(item.getRateApproved() != null ? item.getRateApproved().toString() : "0", dataFont));
            priceCell.setBorder(Rectangle.BOX);
            priceCell.setPadding(3);
            priceCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            itemsTable.addCell(priceCell);

            // Amount
            PdfPCell amountCell = new PdfPCell(new Phrase(item.getTotal() != null ? item.getTotal().toString() : "0", dataFont));
            amountCell.setBorder(Rectangle.BOX);
            amountCell.setPadding(3);
            amountCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            itemsTable.addCell(amountCell);
        }

        document.add(itemsTable);
    }



    /**
     * Add only addresses for T&C page template
     */
    private void addTCPageAddresses(com.itextpdf.text.Document document, PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        // Add spacing to position addresses in the correct boxes on the template
        Paragraph spacer = new Paragraph(" ");
        spacer.setSpacingAfter(100); // Adjust based on template layout
        document.add(spacer);

        // Add only the addresses in the designated boxes
        addAddressBoxes(document, deliveryTerms);
    }

    /**
     * Add address boxes for T&C template
     */
    private void addAddressBoxes(com.itextpdf.text.Document document, PurchaseOrderDeliveryTermsDTO deliveryTerms) throws DocumentException {
        Font normalFont = new Font(Font.FontFamily.HELVETICA, 9, Font.NORMAL);

        // Create two-column layout for addresses
        PdfPTable addressTable = new PdfPTable(2);
        addressTable.setWidthPercentage(100);
        addressTable.setWidths(new float[]{50, 50});

        // Billing/Invoicing Address (Company Address)
        PdfPCell billingCell = new PdfPCell();
        billingCell.setBorder(Rectangle.NO_BORDER); // Template has the border
        billingCell.setPadding(10);

        // Company address content
        Paragraph billingContent = new Paragraph();
        billingContent.add(new Chunk("SYNERGY SHIPBUILDERS N DOCK WORKS LIMITED\n", normalFont));
        billingContent.add(new Chunk("(ISO 9001 - 2015 COMPANY)\n", normalFont));
        billingContent.add(new Chunk("Plot No. 51-52, Sector-25\n", normalFont));
        billingContent.add(new Chunk("Turbhe MIDC, Navi Mumbai - 400705\n", normalFont));
        billingContent.add(new Chunk("Maharashtra, India\n", normalFont));
        billingContent.add(new Chunk("Phone: +91-22-27601234\n", normalFont));
        billingContent.add(new Chunk("Email: <EMAIL>", normalFont));
        billingCell.addElement(billingContent);

        addressTable.addCell(billingCell);

        // Delivery Address
        PdfPCell deliveryCell = new PdfPCell();
        deliveryCell.setBorder(Rectangle.NO_BORDER); // Template has the border
        deliveryCell.setPadding(10);

        // Delivery address content from delivery terms
        Paragraph deliveryContent = new Paragraph();
        if (deliveryTerms.getDeliveryAddress() != null && !deliveryTerms.getDeliveryAddress().trim().isEmpty()) {
            deliveryContent.add(new Chunk(deliveryTerms.getDeliveryAddress(), normalFont));
        } else {
            deliveryContent.add(new Chunk("Delivery Address\nTo be specified", normalFont));
        }
        deliveryCell.addElement(deliveryContent);

        addressTable.addCell(deliveryCell);

        document.add(addressTable);
    }

}
