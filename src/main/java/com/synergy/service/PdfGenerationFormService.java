package com.synergy.service;

import com.synergy.dto.*;
import com.synergy.entity.*;
import com.synergy.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class PdfGenerationFormService {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfGenerationFormService.class);
    
    @Autowired
    private VendorRevisedQuotationRepository vendorRevisedQuotationRepository;
    
    @Autowired
    private VendorRevisedQuotationItemRepository vendorRevisedQuotationItemRepository;
    
    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;
    
    @Autowired
    private VendorQuotationRepository vendorQuotationRepository;
    
    @Autowired
    private VendorQuotationItemRepository vendorQuotationItemRepository;

    @Autowired
    private VendorRepository vendorRepository;
    
    @Autowired
    private PurchaseRequestService purchaseRequestService;
    
    @Autowired
    private VendorRevisedQuotationService vendorRevisedQuotationService;
    
    /**
     * Get PDF generation form data with priority logic:
     * 1. Vendor revised data (if exists)
     * 2. Management edited data (if exists) 
     * 3. Original quotation data
     */
    public PdfGenerationFormDTO getPdfGenerationFormData(String vendorPoId) {
        logger.info("Getting PDF generation form data for vendor PO: {}", vendorPoId);
        
        // Parse vendor PO ID to get PR ID and vendor info
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);
        
        PdfGenerationFormDTO formData = new PdfGenerationFormDTO();
        formData.setVendorPoId(vendorPoId);
        formData.setOriginalPrId(vendorPO.getOriginalPrId());
        formData.setVendorId(vendorPO.getVendorId());
        
        // Priority 1: Check for vendor revised data
        VendorRevisedQuotationDTO vendorRevisedData = vendorRevisedQuotationService.getRevisedQuotationByVendorPoId(vendorPoId);
        if (vendorRevisedData != null) {
            logger.info("Found vendor revised data for vendor PO: {}", vendorPoId);
            populateFromVendorRevisedData(formData, vendorRevisedData);
            formData.setDataSource("VENDOR_REVISED");
            return formData;
        }
        
        // Priority 2: Check for management edited data (same table as vendor revised)
        // This will be implemented when management saves data
        
        // Priority 3: Use original quotation data
        logger.info("Using original quotation data for vendor PO: {}", vendorPoId);
        populateFromOriginalQuotationData(formData, vendorPO);
        formData.setDataSource("ORIGINAL_QUOTATION");
        
        return formData;
    }
    
    /**
     * Save management edited data to the same table as vendor revised quotations
     */
    @Transactional
    public void saveManagementEditedData(PdfGenerationFormDTO formData) {
        logger.info("Saving management edited data for vendor PO: {}", formData.getVendorPoId());
        
        // Get vendor PO details
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(formData.getVendorPoId());
        
        // Check if revised quotation already exists
        Optional<VendorRevisedQuotationEntity> existingRevised = vendorRevisedQuotationRepository
                .findByPurchaseRequestIdAndVendorSrNo(vendorPO.getOriginalPrId(), vendorPO.getVendorId())
                .stream().findFirst();
        
        VendorRevisedQuotationEntity revisedQuotation;
        if (existingRevised.isPresent()) {
            // Update existing
            revisedQuotation = existingRevised.get();
            logger.info("Updating existing revised quotation for vendor PO: {}", formData.getVendorPoId());
        } else {
            // Create new
            revisedQuotation = new VendorRevisedQuotationEntity();
            
            // Get original quotation for reference
            List<VendorQuotationEntity> originalQuotations = vendorQuotationRepository
                    .findByPurchaseRequestIdAndVendorSrNo(vendorPO.getOriginalPrId(), vendorPO.getVendorId());
            
            if (originalQuotations.isEmpty()) {
                throw new IllegalStateException("No original quotation found for vendor PO: " + formData.getVendorPoId());
            }
            
            VendorQuotationEntity originalQuotation = originalQuotations.get(0);
            
            // Set references
            PurchaseRequestEntity pr = purchaseRequestRepository.findById(vendorPO.getOriginalPrId())
                    .orElseThrow(() -> new IllegalArgumentException("Purchase Request not found: " + vendorPO.getOriginalPrId()));
            
            revisedQuotation.setPurchaseRequest(pr);
            revisedQuotation.setVendor(originalQuotation.getVendor());
            revisedQuotation.setOriginalQuotation(originalQuotation);
            
            logger.info("Creating new revised quotation for vendor PO: {}", formData.getVendorPoId());
        }
        
        // Update quotation level data
        revisedQuotation.setSubmissionDate(new Date());
        revisedQuotation.setCurrency(formData.getCurrency());
        revisedQuotation.setStatus("MANAGEMENT_EDITED");
        
        // Calculate totals from line items
        BigDecimal totalCost = formData.getLineItems().stream()
                .map(item -> item.getTotal() != null ? item.getTotal() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        revisedQuotation.setRevisedTotalCost(totalCost.doubleValue());
        revisedQuotation.setRevisedGrandTotal(formData.getRevisedGrandTotal() != null ? 
                formData.getRevisedGrandTotal().doubleValue() : totalCost.doubleValue());
        
        // Save quotation first
        revisedQuotation = vendorRevisedQuotationRepository.save(revisedQuotation);
        
        // Save line items
        saveRevisedLineItems(revisedQuotation, formData.getLineItems());
        
        logger.info("Successfully saved management edited data for vendor PO: {}", formData.getVendorPoId());
    }
    
    private void populateFromVendorRevisedData(PdfGenerationFormDTO formData, VendorRevisedQuotationDTO vendorRevisedData) {
        // Set delivery terms
        formData.setCurrency(vendorRevisedData.getCurrency());

        // Handle revised grand total with null check
        if (vendorRevisedData.getRevisedGrandTotal() != null) {
            formData.setRevisedGrandTotal(BigDecimal.valueOf(vendorRevisedData.getRevisedGrandTotal()));
        }

        // Handle original grand total with null check
        if (vendorRevisedData.getOriginalGrandTotal() != null) {
            formData.setGrandTotal(BigDecimal.valueOf(vendorRevisedData.getOriginalGrandTotal()));
        }

        // Convert line items
        List<PdfGenerationLineItemDTO> lineItems = vendorRevisedData.getItems().stream()
                .map(this::convertVendorRevisedItemToPdfItem)
                .collect(Collectors.toList());

        formData.setLineItems(lineItems);
    }
    
    private void populateFromOriginalQuotationData(PdfGenerationFormDTO formData, VendorSplitPODTO vendorPO) {
        // Set basic info
        formData.setCurrency(vendorPO.getCurrency());
        formData.setGrandTotal(vendorPO.getVendorGrandTotal());
        formData.setRevisedGrandTotal(vendorPO.getVendorRevisedGrandTotal());
        
        // Convert line items from vendor PO
        List<PdfGenerationLineItemDTO> lineItems = vendorPO.getLineItems().stream()
                .map(this::convertApprovedPOItemToPdfItem)
                .collect(Collectors.toList());
        
        formData.setLineItems(lineItems);
    }
    
    private PdfGenerationLineItemDTO convertVendorRevisedItemToPdfItem(VendorRevisedQuotationItemDTO revisedItem) {
        PdfGenerationLineItemDTO pdfItem = new PdfGenerationLineItemDTO();
        pdfItem.setUniqueCode(revisedItem.getUniqueCode());
        pdfItem.setDescription(revisedItem.getProductName());
        pdfItem.setMaterialFamily(revisedItem.getMaterialFamily());
        pdfItem.setQuantity(revisedItem.getQuantity());
        pdfItem.setUnitOfMeasure(revisedItem.getUnitOfMeasure());

        // Handle revised unit price with null check
        if (revisedItem.getRevisedUnitPrice() != null) {
            pdfItem.setUnitPrice(BigDecimal.valueOf(revisedItem.getRevisedUnitPrice()));
            pdfItem.setRateApproved(BigDecimal.valueOf(revisedItem.getRevisedUnitPrice()));
        }

        // Handle revised total cost with null check
        if (revisedItem.getRevisedTotalCost() != null) {
            pdfItem.setTotal(BigDecimal.valueOf(revisedItem.getRevisedTotalCost()));
        }

        // Handle original unit price with null check
        if (revisedItem.getOriginalUnitPrice() != null) {
            pdfItem.setOriginalUnitPrice(BigDecimal.valueOf(revisedItem.getOriginalUnitPrice()));
        }

        // Handle original total cost with null check
        if (revisedItem.getOriginalTotalCost() != null) {
            pdfItem.setOriginalTotal(BigDecimal.valueOf(revisedItem.getOriginalTotalCost()));
        }

        return pdfItem;
    }
    
    private PdfGenerationLineItemDTO convertApprovedPOItemToPdfItem(ApprovedPOLineItemDTO approvedItem) {
        PdfGenerationLineItemDTO pdfItem = new PdfGenerationLineItemDTO();
        pdfItem.setUniqueCode(approvedItem.getUniqueCode());
        pdfItem.setItemCode(approvedItem.getItemCode());
        pdfItem.setDescription(approvedItem.getDescription());
        pdfItem.setMaterialFamily(approvedItem.getMaterialFamily());
        pdfItem.setQuantity(approvedItem.getQuantity());
        pdfItem.setUnitOfMeasure(approvedItem.getUnitOfMeasure());
        pdfItem.setUnitPrice(approvedItem.getRateApproved());
        pdfItem.setRateApproved(approvedItem.getRateApproved());
        pdfItem.setTotal(approvedItem.getTotal());
        pdfItem.setOriginalUnitPrice(approvedItem.getRateApproved());
        pdfItem.setOriginalTotal(approvedItem.getTotal());
        return pdfItem;
    }
    
    private void saveRevisedLineItems(VendorRevisedQuotationEntity revisedQuotation, List<PdfGenerationLineItemDTO> lineItems) {
        // Delete existing items
        List<VendorRevisedQuotationItemEntity> existingItems = vendorRevisedQuotationItemRepository
                .findByRevisedQuotationId(revisedQuotation.getId());
        vendorRevisedQuotationItemRepository.deleteAll(existingItems);
        
        // Create new items
        List<VendorRevisedQuotationItemEntity> newItems = lineItems.stream()
                .map(item -> convertPdfItemToRevisedItem(item, revisedQuotation))
                .collect(Collectors.toList());
        
        vendorRevisedQuotationItemRepository.saveAll(newItems);
    }
    
    private VendorRevisedQuotationItemEntity convertPdfItemToRevisedItem(PdfGenerationLineItemDTO pdfItem, VendorRevisedQuotationEntity revisedQuotation) {
        VendorRevisedQuotationItemEntity item = new VendorRevisedQuotationItemEntity();
        item.setRevisedQuotation(revisedQuotation);
        item.setUniqueCode(pdfItem.getUniqueCode());
        item.setProductName(pdfItem.getDescription());
        item.setQuantity(pdfItem.getQuantity());
        item.setUnitOfMeasure(pdfItem.getUnitOfMeasure());
        item.setOriginalUnitPrice(pdfItem.getOriginalUnitPrice() != null ? pdfItem.getOriginalUnitPrice().doubleValue() : null);
        item.setRevisedUnitPrice(pdfItem.getUnitPrice() != null ? pdfItem.getUnitPrice().doubleValue() : null);
        item.setOriginalTotalCost(pdfItem.getOriginalTotal() != null ? pdfItem.getOriginalTotal().doubleValue() : null);
        item.setRevisedTotalCost(pdfItem.getTotal() != null ? pdfItem.getTotal().doubleValue() : null);
        item.setMaterialFamily(pdfItem.getMaterialFamily());
        return item;
    }
}
