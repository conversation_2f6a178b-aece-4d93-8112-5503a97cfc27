package com.synergy.entity;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "vendor_revised_quotations")
public class VendorRevisedQuotationEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_request_id", nullable = false)
    private PurchaseRequestEntity purchaseRequest;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vendor_sr_no", nullable = false)
    private VendorEntity vendor;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "original_quotation_id", nullable = false)
    private VendorQuotationEntity originalQuotation;
    
    @Column(name = "submission_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date submissionDate;
    
    @Column(name = "currency")
    private String currency;
    
    @Column(name = "stock_point")
    private String stockPoint;
    
    @Column(name = "reference_number")
    private String referenceNumber;
    
    @Column(name = "original_total_cost")
    private Double originalTotalCost;
    
    @Column(name = "revised_total_cost")
    private Double revisedTotalCost;
    
    @Column(name = "original_additional_charges_total")
    private Double originalAdditionalChargesTotal;
    
    @Column(name = "revised_additional_charges_total")
    private Double revisedAdditionalChargesTotal;
    
    @Column(name = "original_grand_total")
    private Double originalGrandTotal;
    
    @Column(name = "revised_grand_total")
    private Double revisedGrandTotal;
    
    @Column(name = "discount_amount")
    private Double discountAmount;
    
    @Column(name = "discount_percentage")
    private Double discountPercentage;
    
    @Column(name = "status")
    private String status = "SUBMITTED";
    
    @OneToMany(mappedBy = "revisedQuotation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<VendorRevisedQuotationItemEntity> items;
    
    @OneToMany(mappedBy = "revisedQuotation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<VendorRevisedQuotationChargeEntity> additionalCharges;
    
    // Constructors
    public VendorRevisedQuotationEntity() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public PurchaseRequestEntity getPurchaseRequest() {
        return purchaseRequest;
    }
    
    public void setPurchaseRequest(PurchaseRequestEntity purchaseRequest) {
        this.purchaseRequest = purchaseRequest;
    }
    
    public VendorEntity getVendor() {
        return vendor;
    }
    
    public void setVendor(VendorEntity vendor) {
        this.vendor = vendor;
    }
    
    public VendorQuotationEntity getOriginalQuotation() {
        return originalQuotation;
    }
    
    public void setOriginalQuotation(VendorQuotationEntity originalQuotation) {
        this.originalQuotation = originalQuotation;
    }
    
    public Date getSubmissionDate() {
        return submissionDate;
    }
    
    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getStockPoint() {
        return stockPoint;
    }
    
    public void setStockPoint(String stockPoint) {
        this.stockPoint = stockPoint;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public Double getOriginalTotalCost() {
        return originalTotalCost;
    }
    
    public void setOriginalTotalCost(Double originalTotalCost) {
        this.originalTotalCost = originalTotalCost;
    }
    
    public Double getRevisedTotalCost() {
        return revisedTotalCost;
    }
    
    public void setRevisedTotalCost(Double revisedTotalCost) {
        this.revisedTotalCost = revisedTotalCost;
    }
    
    public Double getOriginalAdditionalChargesTotal() {
        return originalAdditionalChargesTotal;
    }
    
    public void setOriginalAdditionalChargesTotal(Double originalAdditionalChargesTotal) {
        this.originalAdditionalChargesTotal = originalAdditionalChargesTotal;
    }
    
    public Double getRevisedAdditionalChargesTotal() {
        return revisedAdditionalChargesTotal;
    }
    
    public void setRevisedAdditionalChargesTotal(Double revisedAdditionalChargesTotal) {
        this.revisedAdditionalChargesTotal = revisedAdditionalChargesTotal;
    }
    
    public Double getOriginalGrandTotal() {
        return originalGrandTotal;
    }
    
    public void setOriginalGrandTotal(Double originalGrandTotal) {
        this.originalGrandTotal = originalGrandTotal;
    }
    
    public Double getRevisedGrandTotal() {
        return revisedGrandTotal;
    }
    
    public void setRevisedGrandTotal(Double revisedGrandTotal) {
        this.revisedGrandTotal = revisedGrandTotal;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public List<VendorRevisedQuotationItemEntity> getItems() {
        return items;
    }
    
    public void setItems(List<VendorRevisedQuotationItemEntity> items) {
        this.items = items;
    }
    
    public List<VendorRevisedQuotationChargeEntity> getAdditionalCharges() {
        return additionalCharges;
    }
    
    public void setAdditionalCharges(List<VendorRevisedQuotationChargeEntity> additionalCharges) {
        this.additionalCharges = additionalCharges;
    }
}
