package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

@Entity
@Table(name = "vendor_po_delivery_terms")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorPODeliveryTermsEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "vendor_po_id", unique = true, nullable = false)
    private String vendorPoId; // e.g., "647" or "647-1"
    
    @Column(name = "original_pr_id", nullable = false)
    private Long originalPrId;
    
    @Column(name = "vendor_id", nullable = false)
    private Long vendorId;
    
    @Column(name = "currency")
    private String currency;
    
    @Column(name = "tax_percentage")
    private String taxPercentage;
    
    @Column(name = "advance_payment_percentage")
    private String advancePaymentPercentage;
    
    @Column(name = "delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deliveryDate;
    
    @Column(name = "invoice_address", length = 1000)
    private String invoiceAddress;
    
    @Column(name = "delivery_address", length = 1000)
    private String deliveryAddress;
    
    @Column(name = "sold_to_address", length = 1000)
    private String soldToAddress;
    
    @Column(name = "delivery_terms")
    private String deliveryTerms;
    
    @Column(name = "pdf_generated_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date pdfGeneratedDate;
    
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;
    
    @Column(name = "updated_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDate;
    
    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        pdfGeneratedDate = new Date();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedDate = new Date();
    }
}
