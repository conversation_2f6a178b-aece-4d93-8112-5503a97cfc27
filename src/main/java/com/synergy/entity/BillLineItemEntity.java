package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonBackReference;

import java.math.BigDecimal;

@Entity
@Table(name = "bill_line_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BillLineItemEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "bill_id", nullable = false)
    @JsonBackReference
    private BillEntity bill;
    
    @Column(name = "item_code")
    private String itemCode;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "material_family")
    private String materialFamily;
    
    @Column(name = "qty_ordered")
    private Integer qtyOrdered;
    
    @Column(name = "qty_received")
    private Integer qtyReceived;
    
    @Column(name = "unit_price", precision = 18, scale = 2)
    private BigDecimal unitPrice;
    
    @Column(name = "total_amount", precision = 18, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "status")
    private String status; // Passed, Partial, Missing, Damage
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "remarks")
    private String remarks;
    
    @Column(name = "quality_check_requirement")
    private String qualityCheckRequirement;
    
    @Column(name = "unique_code")
    private String uniqueCode;
}
