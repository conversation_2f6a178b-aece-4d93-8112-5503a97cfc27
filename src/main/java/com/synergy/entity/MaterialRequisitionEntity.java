package com.synergy.entity;

import jakarta.persistence.*;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.util.*;

@Entity
@Table(name = "material_requisitions", indexes = {
    @Index(name = "idx_requisition_id", columnList = "requisition_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_created_date", columnList = "created_date")
})
public class MaterialRequisitionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "requisition_id", unique = true)
    private String requisitionId;

    @Column(name = "yard_number")
    private String yardNumber;

    @Column(name = "contractor_name")
    private String contractorName;

    @ManyToMany
    @JoinTable(
        name = "material_requisition_contractors",
        joinColumns = @JoinColumn(name = "material_requisition_id"),
        inverseJoinColumns = @JoinColumn(name = "contractor_id")
    )
    private List<ContractorEntity> contractors = new ArrayList<>();

    @Column(name = "project_name")
    private String projectName;

    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "status")
    private String status = "PENDING"; // PENDING, APPROVED, REJECTED

    @Column(name = "priority")
    private String priority;

    @OneToMany(mappedBy = "materialRequisition", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<MaterialRequisitionItemEntity> lineItems = new ArrayList<>();

    @OneToOne(mappedBy = "materialRequisition", cascade = CascadeType.ALL)
    @JsonManagedReference
    private PurchaseRequestEntity purchaseRequest;

    @ManyToMany
    @JoinTable(
        name = "material_requisition_vendors",
        joinColumns = @JoinColumn(name = "material_requisition_id"),
        inverseJoinColumns = @JoinColumn(name = "vendor_id")
    )
    private Set<VendorEntity> vendors = new HashSet<>();

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequisitionId() {
        return requisitionId;
    }

    public void setRequisitionId(String requisitionId) {
        this.requisitionId = requisitionId;
    }

    public String getYardNumber() {
        return yardNumber;
    }

    public void setYardNumber(String yardNumber) {
        this.yardNumber = yardNumber;
    }

    public String getContractorName() {
        return contractorName;
    }

    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }

    public List<ContractorEntity> getContractors() {
        return contractors;
    }

    public void setContractors(List<ContractorEntity> contractors) {
        this.contractors = contractors;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public List<MaterialRequisitionItemEntity> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<MaterialRequisitionItemEntity> lineItems) {
        this.lineItems = lineItems;
    }

    public void addLineItem(MaterialRequisitionItemEntity item) {
        lineItems.add(item);
        item.setMaterialRequisition(this);
    }

    public PurchaseRequestEntity getPurchaseRequest() {
        return purchaseRequest;
    }

    public void setPurchaseRequest(PurchaseRequestEntity purchaseRequest) {
        this.purchaseRequest = purchaseRequest;
    }

    public Set<VendorEntity> getVendors() {
        return vendors;
    }

    public void setVendors(Set<VendorEntity> vendors) {
        this.vendors = vendors;
    }

    @Override
    public String toString() {
        return "MaterialRequisitionEntity{" +
                "id=" + id +
                ", requisitionId='" + requisitionId + '\'' +
                ", yardNumber='" + yardNumber + '\'' +
                ", contractorName='" + contractorName + '\'' +
                ", projectName='" + projectName + '\'' +
                ", createdDate=" + createdDate +
                ", status='" + status + '\'' +
                ", lineItems.size=" + (lineItems != null ? lineItems.size() : 0) +
                ", vendors.size=" + (vendors != null ? vendors.size() : 0) +
                '}';
    }
} 