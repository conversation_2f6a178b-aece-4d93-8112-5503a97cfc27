package com.synergy.entity;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "vendor_quotations")
public class VendorQuotationEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "purchase_request_id")
    private PurchaseRequestEntity purchaseRequest;

    @ManyToOne
    @JoinColumn(name = "vendor_id")
    private VendorEntity vendor;

    @OneToMany(mappedBy = "vendorQuotation", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<VendorQuotationItemEntity> items;

    @OneToMany(mappedBy = "vendorQuotation", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<VendorQuotationChargeEntity> additionalCharges;

    @Column(name = "submission_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date submissionDate;

    @Column(name = "total_cost")
    private Double totalCost;

    @Column(name = "status")
    private String status; // SUBMITTED, SELECTED, REJECTED

    @Column(name = "currency")
    private String currency;

    @Column(name = "stock_point")
    private String stockPoint;

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "additional_charges_total")
    private Double additionalChargesTotal;

    @Column(name = "grand_total")
    private Double grandTotal;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public PurchaseRequestEntity getPurchaseRequest() {
        return purchaseRequest;
    }

    public void setPurchaseRequest(PurchaseRequestEntity purchaseRequest) {
        this.purchaseRequest = purchaseRequest;
    }

    public VendorEntity getVendor() {
        return vendor;
    }

    public void setVendor(VendorEntity vendor) {
        this.vendor = vendor;
    }

    public List<VendorQuotationItemEntity> getItems() {
        return items;
    }

    public void setItems(List<VendorQuotationItemEntity> items) {
        this.items = items;
    }

    public List<VendorQuotationChargeEntity> getAdditionalCharges() {
        return additionalCharges;
    }

    public void setAdditionalCharges(List<VendorQuotationChargeEntity> additionalCharges) {
        this.additionalCharges = additionalCharges;
    }

    public Date getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getStockPoint() {
        return stockPoint;
    }

    public void setStockPoint(String stockPoint) {
        this.stockPoint = stockPoint;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public Double getAdditionalChargesTotal() {
        return additionalChargesTotal;
    }

    public void setAdditionalChargesTotal(Double additionalChargesTotal) {
        this.additionalChargesTotal = additionalChargesTotal;
    }

    public Double getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(Double grandTotal) {
        this.grandTotal = grandTotal;
    }
}
