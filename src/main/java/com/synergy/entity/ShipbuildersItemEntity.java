package com.synergy.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "shipbuilders_items")
public class ShipbuildersItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "Item_name", length = 255)
    private String itemName;

    @Column(name = "Category", length = 255)
    private String category; // Production Stage

    @Column(name = "Sub_Category", length = 255)
    private String subCategory; // Material Family

    @Column(name = "Quality_Check")
    private String qualityCheck;

    @Column(name = "Item_Code_Gemba_suggested", length = 255)
    private String itemCode;

    @Column(name = "Specification_1", length = 255)
    private String specification1;

    @Column(name = "Dropdown", length = 255)
    private String dropdown;

    @Column(name = "Specification_2", length = 255)
    private String specification2;

    @Column(name = "Specification_3", length = 255)
    private String specification3;

    @Column(name = "Material_Description", length = 512)
    private String materialDescription;

    @Column(name = "Material", length = 255)
    private String material;

    @Column(name = "Purchasing_UOM")
    private String purchasingUOM;

    @Column(name = "Inventory_UOM")
    private String inventoryUOM;

    @Column(name = "Qty/Pack")
    private Float qtyPerPack;

    @Column(name = "Reorder_Level")
    private Float reorderLevel;

    @Column(name = "Last_Rate")
    private Float lastRate;

    @Column(name = "GST_Slab")
    private Float gstSlab;

    @Column(name = "unit_of_measure", length = 255)
    private String unitOfMeasure;

    @Column(name = "description", length = 255)
    private String description;

    @Column(name = "image_path", length = 512) // Added field for image path
    private String imagePath;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getQualityCheck() {
        return qualityCheck;
    }

    public void setQualityCheck(String qualityCheck) {
        this.qualityCheck = qualityCheck;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getSpecification1() {
        return specification1;
    }

    public void setSpecification1(String specification1) {
        this.specification1 = specification1;
    }

    public String getDropdown() {
        return dropdown;
    }

    public void setDropdown(String dropdown) {
        this.dropdown = dropdown;
    }

    public String getSpecification2() {
        return specification2;
    }

    public void setSpecification2(String specification2) {
        this.specification2 = specification2;
    }

    public String getSpecification3() {
        return specification3;
    }

    public void setSpecification3(String specification3) {
        this.specification3 = specification3;
    }

    public String getMaterialDescription() {
        return materialDescription;
    }

    public void setMaterialDescription(String materialDescription) {
        this.materialDescription = materialDescription;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getPurchasingUOM() {
        return purchasingUOM;
    }

    public void setPurchasingUOM(String purchasingUOM) {
        this.purchasingUOM = purchasingUOM;
    }

    public String getInventoryUOM() {
        return inventoryUOM;
    }

    public void setInventoryUOM(String inventoryUOM) {
        this.inventoryUOM = inventoryUOM;
    }

    public Float getQtyPerPack() {
        return qtyPerPack;
    }

    public void setQtyPerPack(Float qtyPerPack) {
        this.qtyPerPack = qtyPerPack;
    }

    public Float getReorderLevel() {
        return reorderLevel;
    }

    public void setReorderLevel(Float reorderLevel) {
        this.reorderLevel = reorderLevel;
    }

    public Float getLastRate() {
        return lastRate;
    }

    public void setLastRate(Float lastRate) {
        this.lastRate = lastRate;
    }

    public Float getGstSlab() {
        return gstSlab;
    }

    public void setGstSlab(Float gstSlab) {
        this.gstSlab = gstSlab;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }
}