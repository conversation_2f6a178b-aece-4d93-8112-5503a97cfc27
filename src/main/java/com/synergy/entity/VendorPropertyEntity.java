package com.synergy.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "vendor_property")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorPropertyEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "items")
    private String items;
    
    @Column(name = "vendor_type")
    private String vendorType;
}
