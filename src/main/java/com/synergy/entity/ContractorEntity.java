package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import com.fasterxml.jackson.databind.ObjectMapper;

@Entity
@Table(name = "contractor_master")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractorEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sr_no", unique = true, nullable = false)
    private Long srNo;

    @Column(name = "gst_number", length = 20)
    private String gstNumber;

    @Column(name = "pan_no", length = 20)
    private String panNo;

    @Column(name = "contractor_name", length = 100)
    private String vendorName; // This is mapped to companyName in DTO

    @Column(name = "whatsapp_number", length = 20)
    private String whatsappNumber;

    @Column(name = "contact_number", length = 20)
    private String contactNumber;

    @Column(name = "email_id", length = 100)
    private String emailId;

    @Column(name = "address", columnDefinition = "text")
    private String address; // Stores a JSON array of address objects with type

    @Transient
    private transient ObjectMapper objectMapper = new ObjectMapper();

    @Column(name = "country", length = 50)
    private String country;

    @Column(name = "city", length = 50)
    private String city;

    @Column(name = "state", length = 50)
    private String state;

    @Column(name = "contractor_type", length = 50)
    private String vendorType;

    @Column(name = "items", length = 100)
    private String items;

    @Column(name = "contractor_code", length = 20)
    private String vendorCode;

    @Column(name = "credit_days")
    private Integer creditDays;

    @Column(name = "credit_limit", precision = 10, scale = 2)
    private BigDecimal creditLimit;

    @Column(name = "remark", columnDefinition = "text")
    private String remark;

    @Column(name = "contractor_country_type", length = 50)
    private String contractorCountryType;

    @Column(name = "credit_terms", length = 100)
    private String creditTerms;

    @Column(name = "payment_terms", length = 100)
    private String paymentTerms;
}
