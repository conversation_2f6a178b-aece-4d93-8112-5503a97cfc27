package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "vendor_payment_revisions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorPaymentRevisionEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "purchase_request_id", nullable = false)
    private Long purchaseRequestId;
    
    @Column(name = "vendor_id", nullable = false)
    private Long vendorId;
    
    @Column(name = "original_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal originalAmount;
    
    @Column(name = "revised_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal revisedAmount;
    
    @Column(name = "revision_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date revisionDate;
    
    @Column(name = "vendor_po_id")
    private String vendorPoId; // e.g., "647-1"
    
    // Constructor for easy creation
    public VendorPaymentRevisionEntity(Long purchaseRequestId, Long vendorId, 
                                     BigDecimal originalAmount, BigDecimal revisedAmount, 
                                     String vendorPoId) {
        this.purchaseRequestId = purchaseRequestId;
        this.vendorId = vendorId;
        this.originalAmount = originalAmount;
        this.revisedAmount = revisedAmount;
        this.vendorPoId = vendorPoId;
        this.revisionDate = new Date();
    }
}
