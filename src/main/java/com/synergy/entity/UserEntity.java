package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDate;

@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "serial_no", length = 10)
    private String serialNo;

    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username;

    @Column(name = "user_email", unique = true, nullable = false, length = 100)
    private String email;

    @Column(name = "employee_name", nullable = false, length = 100)
    private String employeeName;

    @Column(name = "password", nullable = false, length = 255)
    private String password;

    @Column(name = "role", nullable = false, length = 20)
    private String role;

    @Column(name = "date", nullable = false, columnDefinition = "DATE")
    private LocalDate date;
}