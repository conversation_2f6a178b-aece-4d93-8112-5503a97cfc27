package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonBackReference;

@Entity
@Table(name = "gate_pass_line_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatePassLineItemEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "gate_pass_id", nullable = false)
    @JsonBackReference
    private GatePassEntity gatePass;
    
    @Column(name = "item_code")
    private String itemCode;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "material_family")
    private String materialFamily;
    
    @Column(name = "qty_ordered")
    private Integer qtyOrdered;
    
    @Column(name = "unique_code")
    private String uniqueCode;
    
    @Column(name = "status")
    private String status; // Pending, Partial
}
