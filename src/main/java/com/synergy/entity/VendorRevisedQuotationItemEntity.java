package com.synergy.entity;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "vendor_revised_quotation_items")
public class VendorRevisedQuotationItemEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "revised_quotation_id", nullable = false)
    private VendorRevisedQuotationEntity revisedQuotation;
    
    @Column(name = "unique_code", nullable = false)
    private String uniqueCode;
    
    @Column(name = "product_name", nullable = false)
    private String productName;
    
    @Column(name = "quantity")
    private Integer quantity;
    
    @Column(name = "unit_of_measure")
    private String unitOfMeasure;
    
    @Column(name = "original_unit_price")
    private Double originalUnitPrice;
    
    @Column(name = "revised_unit_price")
    private Double revisedUnitPrice;
    
    @Column(name = "original_total_cost")
    private Double originalTotalCost;
    
    @Column(name = "revised_total_cost")
    private Double revisedTotalCost;
    
    @Column(name = "discount_amount")
    private Double discountAmount;
    
    @Column(name = "discount_percentage")
    private Double discountPercentage;
    
    @Column(name = "delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deliveryDate;
    
    @Column(name = "vendor_remarks")
    private String vendorRemarks;
    
    @Column(name = "material_family")
    private String materialFamily;
    
    // Constructors
    public VendorRevisedQuotationItemEntity() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public VendorRevisedQuotationEntity getRevisedQuotation() {
        return revisedQuotation;
    }
    
    public void setRevisedQuotation(VendorRevisedQuotationEntity revisedQuotation) {
        this.revisedQuotation = revisedQuotation;
    }
    
    public String getUniqueCode() {
        return uniqueCode;
    }
    
    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }
    
    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }
    
    public Double getOriginalUnitPrice() {
        return originalUnitPrice;
    }
    
    public void setOriginalUnitPrice(Double originalUnitPrice) {
        this.originalUnitPrice = originalUnitPrice;
    }
    
    public Double getRevisedUnitPrice() {
        return revisedUnitPrice;
    }
    
    public void setRevisedUnitPrice(Double revisedUnitPrice) {
        this.revisedUnitPrice = revisedUnitPrice;
    }
    
    public Double getOriginalTotalCost() {
        return originalTotalCost;
    }
    
    public void setOriginalTotalCost(Double originalTotalCost) {
        this.originalTotalCost = originalTotalCost;
    }
    
    public Double getRevisedTotalCost() {
        return revisedTotalCost;
    }
    
    public void setRevisedTotalCost(Double revisedTotalCost) {
        this.revisedTotalCost = revisedTotalCost;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public Date getDeliveryDate() {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }
    
    public String getVendorRemarks() {
        return vendorRemarks;
    }
    
    public void setVendorRemarks(String vendorRemarks) {
        this.vendorRemarks = vendorRemarks;
    }
    
    public String getMaterialFamily() {
        return materialFamily;
    }
    
    public void setMaterialFamily(String materialFamily) {
        this.materialFamily = materialFamily;
    }
}
