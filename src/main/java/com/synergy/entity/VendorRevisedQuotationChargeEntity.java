package com.synergy.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "vendor_revised_quotation_charges")
public class VendorRevisedQuotationChargeEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "revised_quotation_id", nullable = false)
    private VendorRevisedQuotationEntity revisedQuotation;
    
    @Column(name = "charge_name", nullable = false)
    private String chargeName;
    
    @Column(name = "original_charge_amount")
    private Double originalChargeAmount;
    
    @Column(name = "revised_charge_amount")
    private Double revisedChargeAmount;
    
    @Column(name = "discount_amount")
    private Double discountAmount;
    
    @Column(name = "discount_percentage")
    private Double discountPercentage;
    
    @Column(name = "charge_type")
    private String chargeType; // e.g., "TRANSPORTATION", "HANDLING", "PACKAGING", etc.
    
    @Column(name = "description")
    private String description;
    
    // Constructors
    public VendorRevisedQuotationChargeEntity() {}
    
    public VendorRevisedQuotationChargeEntity(String chargeName, Double originalChargeAmount, Double revisedChargeAmount) {
        this.chargeName = chargeName;
        this.originalChargeAmount = originalChargeAmount;
        this.revisedChargeAmount = revisedChargeAmount;
        this.discountAmount = originalChargeAmount - revisedChargeAmount;
        if (originalChargeAmount != null && originalChargeAmount > 0) {
            this.discountPercentage = (discountAmount / originalChargeAmount) * 100;
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public VendorRevisedQuotationEntity getRevisedQuotation() {
        return revisedQuotation;
    }
    
    public void setRevisedQuotation(VendorRevisedQuotationEntity revisedQuotation) {
        this.revisedQuotation = revisedQuotation;
    }
    
    public String getChargeName() {
        return chargeName;
    }
    
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }
    
    public Double getOriginalChargeAmount() {
        return originalChargeAmount;
    }
    
    public void setOriginalChargeAmount(Double originalChargeAmount) {
        this.originalChargeAmount = originalChargeAmount;
    }
    
    public Double getRevisedChargeAmount() {
        return revisedChargeAmount;
    }
    
    public void setRevisedChargeAmount(Double revisedChargeAmount) {
        this.revisedChargeAmount = revisedChargeAmount;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public String getChargeType() {
        return chargeType;
    }
    
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
}
