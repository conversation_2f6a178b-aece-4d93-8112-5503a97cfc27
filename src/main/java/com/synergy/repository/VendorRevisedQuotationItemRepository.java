package com.synergy.repository;

import com.synergy.entity.VendorRevisedQuotationItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VendorRevisedQuotationItemRepository extends JpaRepository<VendorRevisedQuotationItemEntity, Long> {
    
    /**
     * Find items by revised quotation ID
     */
    List<VendorRevisedQuotationItemEntity> findByRevisedQuotationId(Long revisedQuotationId);
    
    /**
     * Find items by unique code and revised quotation ID
     */
    List<VendorRevisedQuotationItemEntity> findByUniqueCodeAndRevisedQuotationId(String uniqueCode, Long revisedQuotationId);
}
