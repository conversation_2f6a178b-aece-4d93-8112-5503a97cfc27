package com.synergy.repository;

import com.synergy.entity.VendorQuotationItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface VendorQuotationItemRepository extends JpaRepository<VendorQuotationItemEntity, Long> {
    
    /**
     * Find all items for a specific vendor quotation
     */
    List<VendorQuotationItemEntity> findByVendorQuotationId(Long vendorQuotationId);
    
    /**
     * Delete all items for a specific vendor quotation
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM VendorQuotationItemEntity v WHERE v.vendorQuotation.id = :vendorQuotationId")
    void deleteByVendorQuotationId(@Param("vendorQuotationId") Long vendorQuotationId);
}
