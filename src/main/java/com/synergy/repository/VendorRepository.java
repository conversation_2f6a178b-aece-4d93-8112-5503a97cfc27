package com.synergy.repository;

import com.synergy.entity.VendorEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Repository
public interface VendorRepository extends JpaRepository<VendorEntity, Long> {


    @Query(value = "SELECT * FROM vendor_master WHERE (status = 'ACTIVE' OR status IS NULL) ORDER BY sr_no", nativeQuery = true)
    List<VendorEntity> findAll();

    List<VendorEntity> findByStatusOrderBySrNo(String status);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM vendor_master WHERE sr_no = ?1", nativeQuery = true)
    void deleteVendorById(Long id);

    @Override
    default void deleteById(Long id) {
        deleteVendorById(id);
    }
}