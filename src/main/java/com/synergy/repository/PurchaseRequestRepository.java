package com.synergy.repository;

import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface PurchaseRequestRepository extends JpaRepository<PurchaseRequestEntity, Long> {
	Optional<PurchaseRequestEntity> findByPrId(String prId);

	List<PurchaseRequestEntity> findAllByOrderByCreatedDateDesc();

	List<PurchaseRequestEntity> findByPrTypeIgnoreCase(String prType);

	List<PurchaseRequestEntity> findByVendorsContaining(VendorEntity vendor);

	Page<PurchaseRequestEntity> findAllByOrderByCreatedDateDesc(Pageable pageable);

	List<PurchaseRequestEntity> findByStatus(String status);

	Page<PurchaseRequestEntity> findByStatus(String status, Pageable pageable);
	List<PurchaseRequestEntity> findByStatusIgnoreCase(String status);

	// Optimized query for approved PRs with line items - eliminates N+1 queries
	// Note: Cannot use DISTINCT with JSON columns, so we'll use a different approach
	@Query("SELECT pr FROM PurchaseRequestEntity pr " +
	       "WHERE UPPER(pr.status) = UPPER(:status) " +
	       "ORDER BY pr.createdDate DESC")
	List<PurchaseRequestEntity> findByStatusIgnoreCaseWithLineItems(@Param("status") String status);

	// Ultra-optimized native SQL query for approved vendor POs - gets all data in one query
	@Query(value = "SELECT " +
			"pr.id as pr_id, " +
			"pr.pr_id as pr_id_string, " +
			"pr.created_date, " +
			"pr.contractor_name, " +
			"pr.yard_number, " +
			"pr.project_name, " +
			"pr.priority, " +
			"pr.currency as pr_currency, " +
			"pr.revised_grand_total, " +
			"COALESCE(v.sr_no, prv.vendor_id) as vendor_id, " +
			"COALESCE(v.company_name, v2.company_name) as vendor_company_name, " +
			"COALESCE(v.vendor_name, v2.vendor_name) as vendor_name, " +
			"COALESCE(v.email_id, v2.email_id) as vendor_email, " +
			"COALESCE(v.contact_number, v2.contact_number) as vendor_contact, " +
			"vq.currency as vendor_currency, " +
			"vq.stock_point, " +
			"vq.reference_number, " +
			"COALESCE(COUNT(DISTINCT vqi.id), COUNT(DISTINCT pri.id)) as line_item_count, " +
			"COALESCE(SUM(CASE WHEN vqi.selected = 1 THEN vqi.total_cost ELSE 0 END), SUM(pri.total)) as vendor_grand_total " +
			"FROM purchase_requests pr " +
			"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
			"LEFT JOIN vendor_master v ON v.sr_no = vq.vendor_id " +
			"LEFT JOIN vendor_quotation_items vqi ON vqi.vendor_quotation_id = vq.id AND vqi.selected = 1 " +
			"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
			"LEFT JOIN vendor_master v2 ON v2.sr_no = prv.vendor_id " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"WHERE pr.status = 'APPROVED' " +
			"AND (vqi.selected = 1 OR vq.id IS NULL) " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.priority, pr.currency, pr.revised_grand_total, " +
			"COALESCE(v.sr_no, prv.vendor_id), COALESCE(v.company_name, v2.company_name), COALESCE(v.vendor_name, v2.vendor_name), " +
			"COALESCE(v.email_id, v2.email_id), COALESCE(v.contact_number, v2.contact_number), vq.currency, vq.stock_point, vq.reference_number " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findApprovedVendorPOsNative();


	// Optimized query to fetch PRs by multiple statuses
	@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.status IN :statuses ORDER BY pr.createdDate DESC")
	List<PurchaseRequestEntity> findByStatusIn(List<String> statuses);

	// Optimized query to fetch PRs by status
	@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.status = :status ORDER BY pr.createdDate DESC")
	List<PurchaseRequestEntity> findByStatusOptimized(String status);

	// Ultra-optimized native SQL query for dashboard views
	// This query directly fetches only the essential fields needed for the dashboard
	// and only includes PRs with vendors
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	// Includes RFQ status calculation based on vendor quotations
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"CASE " +
			"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
			"  WHEN COUNT(DISTINCT prv.vendor_id) = ( " +
			"    SELECT COUNT(DISTINCT vq2.id) FROM vendor_quotations vq2 " +
			"    WHERE vq2.purchase_request_id = pr.id " +
			"    AND NOT EXISTS ( " +
			"      SELECT 1 FROM purchase_request_items pri2 " +
			"      LEFT JOIN vendor_quotation_items vqi ON vqi.unique_code = pri2.unique_code AND vqi.vendor_quotation_id = vq2.id " +
			"      WHERE pri2.purchase_request_id = pr.id " +
			"      AND ( " +
			"        (pri2.quantity_updated = 1 AND (vqi.unit_price IS NULL OR vqi.available_quantity IS NULL)) " +
			"        OR ((pri2.quantity_updated IS NULL OR pri2.quantity_updated = 0) AND vqi.unit_price IS NULL) " +
			"      ) " +
			"    ) " +
			"  ) THEN 'Reply Received' " +
			"  ELSE 'Reply Pending' " +
			"END as rfq_status, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
			"(SELECT pr2.priority FROM purchase_requests pr2 WHERE pr2.id = pr.id) as priority " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
			"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
			"WHERE pr.status = :status " +
			"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsByStatusWithVendorsNative(String status);

	// Ultra-optimized native SQL query for In-Progress Management dashboard
	// Fetches PRs with status IN_PROGRESS_MANAGEMENT
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
			"(SELECT pr2.priority FROM purchase_requests pr2 WHERE pr2.id = pr.id) as priority, " +
			"STRING_AGG(pri.material_family, ', ') as material_families " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"WHERE pr.status = 'IN_PROGRESS_MANAGEMENT' " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsForInProgressManagementNative();

	// Ultra-optimized native SQL query for PR Creation dashboard
	// Fetches PRs with status PENDING_VENDOR_SELECTION only
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
			"(SELECT pr2.priority FROM purchase_requests pr2 WHERE pr2.id = pr.id) as priority, " +
			"STRING_AGG(pri.material_family, ', ') as material_families " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"WHERE pr.status = 'PENDING_VENDOR_SELECTION' " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsForCreationNative();

		// Date range filtering methods
		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetween(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate);

		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND pr.status = :status ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatus(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND pr.status IN :statuses ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatusIn(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("statuses") List<String> statuses);

		// Date range filtering with prType support
		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND (:status IS NULL OR pr.status = :status) AND (:prType IS NULL OR pr.prType = :prType) ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatusAndPrType(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);

		// Light DTO queries with date range filtering
		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, " +
				"CASE " +
				"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
				"  WHEN COUNT(DISTINCT prv.vendor_id) = ( " +
				"    SELECT COUNT(DISTINCT vq2.id) FROM vendor_quotations vq2 " +
				"    WHERE vq2.purchase_request_id = pr.id " +
				"    AND NOT EXISTS ( " +
				"      SELECT 1 FROM purchase_request_items pri2 " +
				"      LEFT JOIN vendor_quotation_items vqi ON vqi.unique_code = pri2.unique_code AND vqi.vendor_quotation_id = vq2.id " +
				"      WHERE pri2.purchase_request_id = pr.id " +
				"      AND ( " +
				"        (pri2.quantity_updated = 1 AND (vqi.unit_price IS NULL OR vqi.available_quantity IS NULL)) " +
				"        OR ((pri2.quantity_updated IS NULL OR pri2.quantity_updated = 0) AND vqi.unit_price IS NULL) " +
				"      ) " +
				"    ) " +
				"  ) THEN 'Reply Received' " +
				"  ELSE 'Reply Pending' " +
				"END as rfq_status, " +
				"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
				"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusWithVendorsNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, pr.priority " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type, pr.priority " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		// Light DTO queries with date range, status and prType filtering
		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, " +
				"CASE " +
				"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
				"  WHEN COUNT(DISTINCT prv.vendor_id) = ( " +
				"    SELECT COUNT(DISTINCT vq2.id) FROM vendor_quotations vq2 " +
				"    WHERE vq2.purchase_request_id = pr.id " +
				"    AND NOT EXISTS ( " +
				"      SELECT 1 FROM purchase_request_items pri2 " +
				"      LEFT JOIN vendor_quotation_items vqi ON vqi.unique_code = pri2.unique_code AND vqi.vendor_quotation_id = vq2.id " +
				"      WHERE pri2.purchase_request_id = pr.id " +
				"      AND ( " +
				"        (pri2.quantity_updated = 1 AND (vqi.unit_price IS NULL OR vqi.available_quantity IS NULL)) " +
				"        OR ((pri2.quantity_updated IS NULL OR pri2.quantity_updated = 0) AND vqi.unit_price IS NULL) " +
				"      ) " +
				"    ) " +
				"  ) THEN 'Reply Received' " +
				"  ELSE 'Reply Pending' " +
				"END as rfq_status, " +
				"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
				"(SELECT pr2.priority FROM purchase_requests pr2 WHERE pr2.id = pr.id) as priority " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
				"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND (:prType IS NULL OR pr.pr_type = :prType) " +
				"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusAndPrTypeWithVendorsNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);

		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, pr.priority " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND (:prType IS NULL OR pr.pr_type = :prType) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type, pr.priority " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusAndPrTypeNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);
}
