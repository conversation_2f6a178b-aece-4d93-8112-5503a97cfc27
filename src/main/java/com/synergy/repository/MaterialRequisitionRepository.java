package com.synergy.repository;

import com.synergy.entity.MaterialRequisitionEntity;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface MaterialRequisitionRepository extends JpaRepository<MaterialRequisitionEntity, Long> {
    List<MaterialRequisitionEntity> findAllByOrderByCreatedDateDesc();
    Page<MaterialRequisitionEntity> findAllByOrderByCreatedDateDesc(Pageable pageable);
    Optional<MaterialRequisitionEntity> findByRequisitionId(String requisitionId);
} 