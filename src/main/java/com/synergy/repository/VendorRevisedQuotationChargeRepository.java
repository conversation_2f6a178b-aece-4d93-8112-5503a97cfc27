package com.synergy.repository;

import com.synergy.entity.VendorRevisedQuotationChargeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VendorRevisedQuotationChargeRepository extends JpaRepository<VendorRevisedQuotationChargeEntity, Long> {
    
    /**
     * Find charges by revised quotation ID
     */
    List<VendorRevisedQuotationChargeEntity> findByRevisedQuotationId(Long revisedQuotationId);
}
