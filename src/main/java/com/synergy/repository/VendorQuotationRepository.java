package com.synergy.repository;

import com.synergy.entity.VendorQuotationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VendorQuotationRepository extends JpaRepository<VendorQuotationEntity, Long> {
    List<VendorQuotationEntity> findByPurchaseRequestId(Long purchaseRequestId);
    List<VendorQuotationEntity> findByVendorSrNo(Long srNo);
    List<VendorQuotationEntity> findByPurchaseRequestIdAndVendorSrNo(Long purchaseRequestId, Long vendorSrNo);

    // Batch method for performance optimization
    List<VendorQuotationEntity> findByPurchaseRequestIdIn(List<Long> purchaseRequestIds);

    // Optimized batch query with JOIN FETCH to eliminate N+1 queries
    // Note: Removed DISTINCT to avoid issues with JSON columns in related entities
    @Query("SELECT vq FROM VendorQuotationEntity vq " +
           "JOIN FETCH vq.vendor " +
           "JOIN FETCH vq.items " +
           "WHERE vq.purchaseRequest.id IN :prIds")
    List<VendorQuotationEntity> findByPurchaseRequestIdsWithVendorAndItems(@Param("prIds") List<Long> prIds);

    // Optimized single PR query with JOIN FETCH
    // Note: Removed DISTINCT to avoid issues with JSON columns in related entities
    @Query("SELECT vq FROM VendorQuotationEntity vq " +
           "JOIN FETCH vq.vendor " +
           "JOIN FETCH vq.items " +
           "WHERE vq.purchaseRequest.id = :prId")
    List<VendorQuotationEntity> findByPurchaseRequestIdWithVendorAndItems(@Param("prId") Long prId);
}
