package com.synergy.repository;

import com.synergy.entity.YardMasterEntity;
import com.synergy.dto.YardMasterDropdownDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface YardMasterRepository extends JpaRepository<YardMasterEntity, Long> {
    
    /**
     * Find yard by yard number
     */
    Optional<YardMasterEntity> findByYardNumber(String yardNumber);
    
    /**
     * Find yards by client name (case insensitive)
     */
    List<YardMasterEntity> findByClientNameContainingIgnoreCase(String clientName);
    
    /**
     * Find yards by project name (case insensitive)
     */
    List<YardMasterEntity> findByProjectNameContainingIgnoreCase(String projectName);
    

    

    
    /**
     * Check if yard number exists
     */
    boolean existsByYardNumber(String yardNumber);
    
    /**
     * Check if yard number exists excluding current id (for updates)
     */
    boolean existsByYardNumberAndIdNot(String yardNumber, Long id);
    
    /**
     * Get lightweight dropdown data for all yards
     */
    @Query("SELECT new com.synergy.dto.YardMasterDropdownDTO(y.yardNumber, y.clientName, y.projectName) " +
           "FROM YardMasterEntity y ORDER BY y.yardNumber ASC")
    List<YardMasterDropdownDTO> findAllForDropdown();
    
    /**
     * Search yards by multiple criteria
     */
    @Query("SELECT y FROM YardMasterEntity y WHERE " +
           "(:yardNumber IS NULL OR UPPER(y.yardNumber) LIKE UPPER(CONCAT('%', :yardNumber, '%'))) AND " +
           "(:clientName IS NULL OR UPPER(y.clientName) LIKE UPPER(CONCAT('%', :clientName, '%'))) AND " +
           "(:projectName IS NULL OR UPPER(y.projectName) LIKE UPPER(CONCAT('%', :projectName, '%'))) " +
           "ORDER BY y.yardNumber ASC")
    List<YardMasterEntity> searchYards(@Param("yardNumber") String yardNumber,
                                       @Param("clientName") String clientName,
                                       @Param("projectName") String projectName);
}
