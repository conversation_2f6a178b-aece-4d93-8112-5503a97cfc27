package com.synergy.repository;

import com.synergy.entity.GRNEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GRNRepository extends JpaRepository<GRNEntity, Long> {
    
    /**
     * Find GRN by GRN number
     */
    Optional<GRNEntity> findByGrnNumber(String grnNumber);
    
    /**
     * Find GRN by gate pass ID
     */
    Optional<GRNEntity> findByGatePassId(String gatePassId);
    
    /**
     * Find all GRNs for a specific vendor PO
     */
    List<GRNEntity> findByVendorPoId(String vendorPoId);
    
    /**
     * Find all GRNs by status
     */
    List<GRNEntity> findByStatusIgnoreCase(String status);
    
    /**
     * Check if GRN exists for gate pass
     */
    boolean existsByGatePassId(String gatePassId);
    
    /**
     * Get the next GRN sequence number
     */
    @Query("SELECT COUNT(g) + 1 FROM GRNEntity g")
    Long getNextGrnSequence();
    
    /**
     * Find all GRNs ordered by created date descending
     */
    List<GRNEntity> findAllByOrderByCreatedDateDesc();
    
    /**
     * Find GRNs by status ordered by created date descending
     */
    List<GRNEntity> findByStatusIgnoreCaseOrderByCreatedDateDesc(String status);
}
