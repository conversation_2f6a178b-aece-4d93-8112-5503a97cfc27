package com.synergy.repository;

import com.synergy.dto.ShipbuildersItemDropdownDTO;
import com.synergy.entity.ShipbuildersItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface ShipbuildersItemRepository extends JpaRepository<ShipbuildersItemEntity, Long> {
	@Query("SELECT s FROM ShipbuildersItemEntity s WHERE LOWER(s.itemName) LIKE LOWER(CONCAT('%', :search, '%'))")
	List<ShipbuildersItemEntity> searchByItemName(@Param("search") String search);

	@Query("SELECT s FROM ShipbuildersItemEntity s WHERE LOWER(s.itemCode) LIKE LOWER(CONCAT('%', :search, '%'))")
	List<ShipbuildersItemEntity> searchByItemCode(@Param("search") String search);

	List<ShipbuildersItemEntity> findBySubCategoryContainingIgnoreCase(String subCategory);

	@Query("SELECT DISTINCT s.subCategory FROM ShipbuildersItemEntity s WHERE s.subCategory IS NOT NULL")
	List<String> findAllSubCategories();

	@Query("SELECT s FROM ShipbuildersItemEntity s WHERE "
			+ "(:subCategory IS NULL OR LOWER(s.subCategory) LIKE LOWER(CONCAT('%', :subCategory, '%'))) AND "
			+ "(:description IS NULL OR LOWER(s.description) LIKE LOWER(CONCAT('%', :description, '%'))) AND "
			+ "(:itemCode IS NULL OR LOWER(s.itemCode) LIKE LOWER(CONCAT('%', :itemCode, '%')))")
	List<ShipbuildersItemEntity> searchItems(@Param("subCategory") String subCategory,
			@Param("description") String description, @Param("itemCode") String itemCode);

	@Query("SELECT new com.synergy.dto.ShipbuildersItemDropdownDTO(s.id, s.itemName, s.itemCode, TRIM(s.materialDescription), s.purchasingUOM, s.subCategory) FROM ShipbuildersItemEntity s")
	List<ShipbuildersItemDropdownDTO> findAllForDropdown();

	@Query("SELECT new com.synergy.dto.ShipbuildersItemDropdownDTO(s.id, s.itemName, s.itemCode, TRIM(s.materialDescription), s.purchasingUOM, s.subCategory) FROM ShipbuildersItemEntity s WHERE s.subCategory = :subCategory")
	List<ShipbuildersItemDropdownDTO> findDropdownBySubCategory(@Param("subCategory") String subCategory);

	Optional<ShipbuildersItemEntity> findByItemCode(String itemCode);
}