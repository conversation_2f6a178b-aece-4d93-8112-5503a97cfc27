package com.synergy.repository;

import com.synergy.entity.VendorBiddingTokenEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VendorBiddingTokenRepository extends JpaRepository<VendorBiddingTokenEntity, Long> {
    
    /**
     * Find a token by its token string
     */
    Optional<VendorBiddingTokenEntity> findByToken(String token);
    
    /**
     * Find all tokens for a specific purchase request and vendor
     */
    List<VendorBiddingTokenEntity> findByPurchaseRequestIdAndVendorSrNo(Long purchaseRequestId, Long vendorId);
    
    /**
     * Find all unused tokens for a specific purchase request and vendor
     */
    @Query("SELECT t FROM VendorBiddingTokenEntity t WHERE t.purchaseRequest.id = :prId AND t.vendor.srNo = :vendorId AND t.isUsed = false")
    List<VendorBiddingTokenEntity> findUnusedTokensByPurchaseRequestAndVendor(@Param("prId") Long purchaseRequestId, @Param("vendorId") Long vendorId);
    
    /**
     * Find all tokens for a specific purchase request
     */
    List<VendorBiddingTokenEntity> findByPurchaseRequestId(Long purchaseRequestId);
    
    /**
     * Find all tokens for a specific vendor
     */
    List<VendorBiddingTokenEntity> findByVendorSrNo(Long vendorId);
    
    /**
     * Check if a token exists and is unused
     */
    @Query("SELECT COUNT(t) > 0 FROM VendorBiddingTokenEntity t WHERE t.token = :token AND t.isUsed = false")
    boolean existsByTokenAndIsUsedFalse(@Param("token") String token);
    
    /**
     * Mark all unused tokens as used for a specific PR and vendor
     */
    @Query("UPDATE VendorBiddingTokenEntity t SET t.isUsed = true, t.usedDate = CURRENT_TIMESTAMP WHERE t.purchaseRequest.id = :prId AND t.vendor.srNo = :vendorId AND t.isUsed = false")
    void markAllTokensAsUsedForPRAndVendor(@Param("prId") Long purchaseRequestId, @Param("vendorId") Long vendorId);
}
