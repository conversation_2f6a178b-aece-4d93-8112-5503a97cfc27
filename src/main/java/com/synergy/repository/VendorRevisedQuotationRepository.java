package com.synergy.repository;

import com.synergy.entity.VendorRevisedQuotationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VendorRevisedQuotationRepository extends JpaRepository<VendorRevisedQuotationEntity, Long> {
    
    /**
     * Find revised quotations by purchase request ID
     */
    List<VendorRevisedQuotationEntity> findByPurchaseRequestId(Long purchaseRequestId);
    
    /**
     * Find revised quotations by vendor ID
     */
    List<VendorRevisedQuotationEntity> findByVendorSrNo(Long vendorSrNo);
    
    /**
     * Find revised quotation by purchase request ID and vendor ID
     */
    List<VendorRevisedQuotationEntity> findByPurchaseRequestIdAndVendorSrNo(Long purchaseRequestId, Long vendorSrNo);
    
    /**
     * Find revised quotation by original quotation ID
     */
    Optional<VendorRevisedQuotationEntity> findByOriginalQuotationId(Long originalQuotationId);
    
    /**
     * Find revised quotations by purchase request ID and vendor ID with original quotation details
     */
    @Query("SELECT vrq FROM VendorRevisedQuotationEntity vrq " +
           "JOIN FETCH vrq.originalQuotation oq " +
           "WHERE vrq.purchaseRequest.id = :purchaseRequestId AND vrq.vendor.srNo = :vendorSrNo")
    List<VendorRevisedQuotationEntity> findByPurchaseRequestIdAndVendorSrNoWithOriginalQuotation(
            @Param("purchaseRequestId") Long purchaseRequestId, 
            @Param("vendorSrNo") Long vendorSrNo);
    
    /**
     * Find all revised quotations for multiple purchase request IDs (for batch operations)
     */
    List<VendorRevisedQuotationEntity> findByPurchaseRequestIdIn(List<Long> purchaseRequestIds);
    
    /**
     * Check if a revised quotation exists for a specific original quotation
     */
    boolean existsByOriginalQuotationId(Long originalQuotationId);
    
    /**
     * Find revised quotations by status
     */
    List<VendorRevisedQuotationEntity> findByStatus(String status);
    
    /**
     * Find revised quotations by purchase request ID and status
     */
    List<VendorRevisedQuotationEntity> findByPurchaseRequestIdAndStatus(Long purchaseRequestId, String status);
    
    /**
     * Get revised quotations with discount information for PDF generation
     */
    @Query("SELECT vrq FROM VendorRevisedQuotationEntity vrq " +
           "JOIN FETCH vrq.items " +
           "JOIN FETCH vrq.additionalCharges " +
           "WHERE vrq.purchaseRequest.id = :purchaseRequestId")
    List<VendorRevisedQuotationEntity> findByPurchaseRequestIdWithItemsAndCharges(@Param("purchaseRequestId") Long purchaseRequestId);
}
