package com.synergy.repository;

import com.synergy.entity.GatePassLineItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GatePassLineItemRepository extends JpaRepository<GatePassLineItemEntity, Long> {
    
    /**
     * Find all line items for a specific gate pass
     */
    List<GatePassLineItemEntity> findByGatePassId(Long gatePassId);
    
    /**
     * Find all line items for a specific gate pass by gate pass ID string
     */
    List<GatePassLineItemEntity> findByGatePass_GatePassId(String gatePassId);
}
