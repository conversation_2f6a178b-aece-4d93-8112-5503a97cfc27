package com.synergy.repository;

import com.synergy.entity.BillEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BillRepository extends JpaRepository<BillEntity, Long> {
    
    /**
     * Find bill by bill number
     */
    Optional<BillEntity> findByBillNumber(String billNumber);
    
    /**
     * Find bill by GRN ID
     */
    Optional<BillEntity> findByGrnId(Long grnId);
    
    /**
     * Find all bills for a specific vendor PO
     */
    List<BillEntity> findByVendorPoId(String vendorPoId);
    
    /**
     * Find all bills by status
     */
    List<BillEntity> findByStatusIgnoreCase(String status);
    
    /**
     * Check if bill exists for GRN
     */
    boolean existsByGrnId(Long grnId);
    
    /**
     * Get the next bill sequence number
     */
    @Query("SELECT COUNT(b) + 1 FROM BillEntity b")
    Long getNextBillSequence();
    
    /**
     * Find all bills ordered by created date descending
     */
    List<BillEntity> findAllByOrderByCreatedDateDesc();
    
    /**
     * Find bills by status ordered by created date descending
     */
    List<BillEntity> findByStatusIgnoreCaseOrderByCreatedDateDesc(String status);
    
    /**
     * Find bills by vendor company name
     */
    List<BillEntity> findByVendorCompanyNameContainingIgnoreCase(String vendorCompanyName);
}
