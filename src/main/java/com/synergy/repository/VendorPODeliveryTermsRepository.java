package com.synergy.repository;

import com.synergy.entity.VendorPODeliveryTermsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VendorPODeliveryTermsRepository extends JpaRepository<VendorPODeliveryTermsEntity, Long> {
    
    /**
     * Find delivery terms by vendor PO ID
     */
    Optional<VendorPODeliveryTermsEntity> findByVendorPoId(String vendorPoId);
    
    /**
     * Find all delivery terms for a specific original PR
     */
    List<VendorPODeliveryTermsEntity> findByOriginalPrId(Long originalPrId);
    
    /**
     * Find all delivery terms for a specific vendor
     */
    List<VendorPODeliveryTermsEntity> findByVendorId(Long vendorId);
    
    /**
     * Check if delivery terms exist for vendor PO
     */
    boolean existsByVendorPoId(String vendorPoId);
}
