package com.synergy.repository;

import com.synergy.entity.VendorPaymentRevisionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VendorPaymentRevisionRepository extends JpaRepository<VendorPaymentRevisionEntity, Long> {
    
    /**
     * Find all payment revisions for a specific purchase request
     */
    List<VendorPaymentRevisionEntity> findByPurchaseRequestId(Long purchaseRequestId);
    
    /**
     * Find payment revision for a specific PR and vendor
     */
    Optional<VendorPaymentRevisionEntity> findByPurchaseRequestIdAndVendorId(Long purchaseRequestId, Long vendorId);
    
    /**
     * Find payment revision by vendor PO ID
     */
    Optional<VendorPaymentRevisionEntity> findByVendorPoId(String vendorPoId);
    
    /**
     * Delete payment revision for specific PR and vendor
     */
    void deleteByPurchaseRequestIdAndVendorId(Long purchaseRequestId, Long vendorId);

    /**
     * Find all vendor payment revisions for multiple purchase requests (for batch operations)
     */
    List<VendorPaymentRevisionEntity> findByPurchaseRequestIdIn(List<Long> purchaseRequestIds);
}
