package com.synergy.dto;

import java.util.Date;
import java.util.List;

public class VendorRevisedQuotationDTO {
    private Long id;
    private Long purchaseRequestId;
    private Long vendorId;
    private String vendorName;
    private String vendorCompanyName;
    private Long originalQuotationId;
    private Date submissionDate;
    private String currency;
    private String stockPoint;
    private String referenceNumber;
    
    // Original values
    private Double originalTotalCost;
    private Double originalAdditionalChargesTotal;
    private Double originalGrandTotal;
    
    // Revised values
    private Double revisedTotalCost;
    private Double revisedAdditionalChargesTotal;
    private Double revisedGrandTotal;
    
    // Discount information
    private Double discountAmount;
    private Double discountPercentage;
    
    private String status;
    private String priority;
    
    private List<VendorRevisedQuotationItemDTO> items;
    private List<VendorRevisedQuotationChargeDTO> additionalCharges;
    
    // Constructors
    public VendorRevisedQuotationDTO() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getPurchaseRequestId() {
        return purchaseRequestId;
    }
    
    public void setPurchaseRequestId(Long purchaseRequestId) {
        this.purchaseRequestId = purchaseRequestId;
    }
    
    public Long getVendorId() {
        return vendorId;
    }
    
    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }
    
    public String getVendorName() {
        return vendorName;
    }
    
    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }
    
    public String getVendorCompanyName() {
        return vendorCompanyName;
    }
    
    public void setVendorCompanyName(String vendorCompanyName) {
        this.vendorCompanyName = vendorCompanyName;
    }
    
    public Long getOriginalQuotationId() {
        return originalQuotationId;
    }
    
    public void setOriginalQuotationId(Long originalQuotationId) {
        this.originalQuotationId = originalQuotationId;
    }
    
    public Date getSubmissionDate() {
        return submissionDate;
    }
    
    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getStockPoint() {
        return stockPoint;
    }
    
    public void setStockPoint(String stockPoint) {
        this.stockPoint = stockPoint;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public Double getOriginalTotalCost() {
        return originalTotalCost;
    }
    
    public void setOriginalTotalCost(Double originalTotalCost) {
        this.originalTotalCost = originalTotalCost;
    }
    
    public Double getOriginalAdditionalChargesTotal() {
        return originalAdditionalChargesTotal;
    }
    
    public void setOriginalAdditionalChargesTotal(Double originalAdditionalChargesTotal) {
        this.originalAdditionalChargesTotal = originalAdditionalChargesTotal;
    }
    
    public Double getOriginalGrandTotal() {
        return originalGrandTotal;
    }
    
    public void setOriginalGrandTotal(Double originalGrandTotal) {
        this.originalGrandTotal = originalGrandTotal;
    }
    
    public Double getRevisedTotalCost() {
        return revisedTotalCost;
    }
    
    public void setRevisedTotalCost(Double revisedTotalCost) {
        this.revisedTotalCost = revisedTotalCost;
    }
    
    public Double getRevisedAdditionalChargesTotal() {
        return revisedAdditionalChargesTotal;
    }
    
    public void setRevisedAdditionalChargesTotal(Double revisedAdditionalChargesTotal) {
        this.revisedAdditionalChargesTotal = revisedAdditionalChargesTotal;
    }
    
    public Double getRevisedGrandTotal() {
        return revisedGrandTotal;
    }
    
    public void setRevisedGrandTotal(Double revisedGrandTotal) {
        this.revisedGrandTotal = revisedGrandTotal;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    public List<VendorRevisedQuotationItemDTO> getItems() {
        return items;
    }
    
    public void setItems(List<VendorRevisedQuotationItemDTO> items) {
        this.items = items;
    }
    
    public List<VendorRevisedQuotationChargeDTO> getAdditionalCharges() {
        return additionalCharges;
    }
    
    public void setAdditionalCharges(List<VendorRevisedQuotationChargeDTO> additionalCharges) {
        this.additionalCharges = additionalCharges;
    }
}
