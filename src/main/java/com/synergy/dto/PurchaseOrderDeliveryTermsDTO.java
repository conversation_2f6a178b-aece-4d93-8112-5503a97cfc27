package com.synergy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for delivery terms used in Purchase Order PDF generation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for purchase order delivery terms")
public class PurchaseOrderDeliveryTermsDTO {
    
    @Schema(description = "Currency for the purchase order", example = "INR")
    private String currency;
    
    @Schema(description = "Tax percentage", example = "18")
    private String taxPercentage;
    
    @Schema(description = "Advance payment percentage", example = "30")
    private String advancePaymentPercentage;

    @Schema(description = "Payment terms", example = "30% advance, 70% on delivery")
    private String paymentTerms;

    @Schema(description = "Delivery date")
    private Date deliveryDate;

    @Schema(description = "Invoice address - Bill To", example = "Synergy Shipbuilders Yard, Gauaudaulim, Cumbarjua, Tiswadi, Ilhas – Goa, India")
    private String invoiceAddress;

    @Schema(description = "Delivery address - Ship To", example = "Synergy Shipbuilders Yard, Gauaudaulim, Cumbarjua, Tiswadi, Ilhas – Goa, India")
    private String deliveryAddress;

    @Schema(description = "Sold To address", example = "Synergy Shipbuilders Yard, Gauaudaulim, Cumbarjua, Tiswadi, Ilhas – Goa, India")
    private String soldToAddress;

    @Schema(description = "Delivery terms selected from dropdown", example = "FOB, CIF, Ex-Works, As per agreement")
    private String deliveryTerms;

    @Schema(description = "Delivery location (deprecated - use deliveryAddress)", example = "Goa, India")
    @Deprecated
    private String deliveryLocation;

    @Schema(description = "Purchase request ID", example = "1")
    private Long purchaseRequestId;
}
