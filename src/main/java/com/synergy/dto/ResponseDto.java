package com.synergy.dto;

import lombok.Data;

@Data
public class ResponseDto<T> {
    private boolean success;
    private String message;
    private T data;
    private int statusCode;

    public ResponseDto(boolean success, String message, T data, int statusCode) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.statusCode = statusCode;
    }

    public static <T> ResponseDto<T> success(T data, String message) {
        return new ResponseDto<>(true, message, data, 200);
    }

    public static <T> ResponseDto<T> error(String message, int statusCode) {
        return new ResponseDto<>(false, message, null, statusCode);
    }
} 