package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PurchaseRequestItemDTO {
    private Long id;
    private String uniqueCode;
    private String productName;
    private String unitOfMeasure;
    private Integer quantity;
    private Integer changeInQuantity;
    private BigDecimal rate;
    private BigDecimal total;
    private String itemName;
    private Boolean quantityUpdated;
    private String materialFamily;
    private String remarks;
    private String attachments;
}
