package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractorDTO {
    private Long srNo;
    private String companyName; // This will be mapped to contractor_name in the service
    private String gstNumber;
    private String panNo;
    private String vendorName;
    private String whatsappNumber;
    private String contactNumber;
    private String emailId;
    private List<AddressDTO> addresses; // List of address objects with type
    private String items;
    private String vendorCode;
    private Integer creditDays;
    private BigDecimal creditLimit;
    private String remark;
    private String contractorCountryType;
    private String creditTerms;
    private String paymentTerms;
}
