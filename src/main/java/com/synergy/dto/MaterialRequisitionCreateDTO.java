package com.synergy.dto;

import java.util.List;
import com.synergy.entity.MaterialRequisitionItemEntity;

public class MaterialRequisitionCreateDTO {
    private String yardNumber;
    private List<String> contractorNames;
    private String projectName;
    private String priority;
    private List<MaterialRequisitionItemEntity> lineItems;

    // Getters and Setters
    public String getYardNumber() {
        return yardNumber;
    }

    public void setYardNumber(String yardNumber) {
        this.yardNumber = yardNumber;
    }

    public List<String> getContractorNames() {
        return contractorNames;
    }

    public void setContractorNames(List<String> contractorNames) {
        this.contractorNames = contractorNames;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public List<MaterialRequisitionItemEntity> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<MaterialRequisitionItemEntity> lineItems) {
        this.lineItems = lineItems;
    }
}
