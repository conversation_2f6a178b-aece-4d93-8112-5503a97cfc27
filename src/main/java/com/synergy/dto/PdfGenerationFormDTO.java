package com.synergy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "DTO for PDF generation form data")
public class PdfGenerationFormDTO {
    
    @Schema(description = "Vendor PO ID", example = "647-1")
    private String vendorPoId;
    
    @Schema(description = "Original PR ID", example = "647")
    private Long originalPrId;
    
    @Schema(description = "Vendor ID", example = "123")
    private Long vendorId;
    
    // Delivery Terms Fields
    @Schema(description = "Currency", example = "INR")
    private String currency;
    
    @Schema(description = "Delivery terms", example = "FOB")
    private String deliveryTerms;
    
    @Schema(description = "Payment terms", example = "30% advance, 70% on delivery")
    private String paymentTerms;
    
    @Schema(description = "Tax percentage", example = "18")
    private String taxPercentage;
    
    @Schema(description = "Delivery date")
    private Date deliveryDate;
    
    @Schema(description = "Delivery location", example = "Goa, India")
    private String deliveryLocation;
    
    @Schema(description = "Sold to address")
    private String soldToAddress;
    
    @Schema(description = "Invoice address")
    private String invoiceAddress;
    
    @Schema(description = "Delivery address")
    private String deliveryAddress;
    
    // Line Items
    @Schema(description = "List of line items")
    private List<PdfGenerationLineItemDTO> lineItems;
    
    @Schema(description = "Grand total", example = "750000.00")
    private BigDecimal grandTotal;
    
    @Schema(description = "Revised grand total", example = "745000.00")
    private BigDecimal revisedGrandTotal;
    
    @Schema(description = "Data source type", example = "VENDOR_REVISED", allowableValues = {"VENDOR_REVISED", "MANAGEMENT_EDITED", "ORIGINAL_QUOTATION"})
    private String dataSource;
}
