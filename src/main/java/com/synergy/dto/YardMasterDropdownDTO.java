package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Lightweight DTO for yard master dropdown")
public class YardMasterDropdownDTO {
    
    @Schema(description = "Unique yard number", example = "YD-101")
    private String yardNumber;
    
    @Schema(description = "Name of the client", example = "Oceanic Shipbuilders")
    private String clientName;
    
    @Schema(description = "Name of the project", example = "Speed Boat")
    private String projectName;
}
