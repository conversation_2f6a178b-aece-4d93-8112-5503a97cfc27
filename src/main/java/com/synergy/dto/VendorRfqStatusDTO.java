package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorRfqStatusDTO {
    private Long vendorId;
    private String companyName;
    private String vendorName;
    private String emailId;
    private String contactNumber;
    private boolean hasResponded; // true if vendor has submitted quotation
    private String responseStatus; // "Responded", "Not Responded"
}
