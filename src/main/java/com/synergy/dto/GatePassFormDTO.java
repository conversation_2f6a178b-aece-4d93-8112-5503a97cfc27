package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatePassFormDTO {
    
    private String gatePassId;
    private String vendorPoId;
    private Long originalPrId;
    private String originalPrIdString;
    private Long vendorId;
    private String vendorCompanyName;
    private String vendorName;
    private String vendorEmail;
    private String vendorContactNumber;
    private String yardNumber;
    private String projectName;
    private String contractorName;
    private Date expectedDate;
    private Integer lineItemCount;
    
    // Form fields
    private String invoiceNumber;
    private String vehicleNumber;
    private String driverName;
    private List<String> vehiclePhotos; // Array of base64 image data
    
    // Line items
    private List<GatePassLineItemDTO> lineItems;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GatePassLineItemDTO {
        private String uniqueCode;
        private String itemCode;
        private String description;
        private String materialFamily;
        private Double quantity;
        private String unit;
        private Double rate;
        private Double amount;
    }
}
