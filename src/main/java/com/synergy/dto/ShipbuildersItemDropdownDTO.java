package com.synergy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipbuildersItemDropdownDTO {
    private Long id;
    private String itemName;
    private String itemCode;
    private String description;
    private String unitOfMeasure;
    private String subCategory;

    // Custom setter to ensure description is always trimmed
    public void setDescription(String description) {
        this.description = description != null ? description.trim() : null;
    }

}
