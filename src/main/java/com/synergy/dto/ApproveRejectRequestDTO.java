package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for approving or rejecting a purchase request")
public class ApproveRejectRequestDTO {
    @Schema(description = "Action to perform on the purchase request", example = "APPROVE",
            allowableValues = {"APPROVE", "REJECT"}, required = true)
    private String action; // "APPROVE" or "REJECT"

    @Schema(description = "Reason for rejection (required only when action is REJECT)",
            example = "Insufficient budget allocation")
    private String rejectionReason; // Only required if action is "REJECT"
}
