package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

public class VendorRevisedQuotationItemDTO {
    private Long id;
    private String uniqueCode;
    private String productName;
    private Integer quantity;
    private String unitOfMeasure;
    
    // Original values
    private Double originalUnitPrice;
    private Double originalTotalCost;
    
    // Revised values
    private Double revisedUnitPrice;
    private Double revisedTotalCost;
    
    // Discount information
    private Double discountAmount;
    private Double discountPercentage;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", shape = JsonFormat.Shape.STRING)
    private Date deliveryDate;
    
    private String vendorRemarks;
    private String materialFamily;
    
    // Constructors
    public VendorRevisedQuotationItemDTO() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUniqueCode() {
        return uniqueCode;
    }
    
    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }
    
    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }
    
    public Double getOriginalUnitPrice() {
        return originalUnitPrice;
    }
    
    public void setOriginalUnitPrice(Double originalUnitPrice) {
        this.originalUnitPrice = originalUnitPrice;
    }
    
    public Double getOriginalTotalCost() {
        return originalTotalCost;
    }
    
    public void setOriginalTotalCost(Double originalTotalCost) {
        this.originalTotalCost = originalTotalCost;
    }
    
    public Double getRevisedUnitPrice() {
        return revisedUnitPrice;
    }
    
    public void setRevisedUnitPrice(Double revisedUnitPrice) {
        this.revisedUnitPrice = revisedUnitPrice;
    }
    
    public Double getRevisedTotalCost() {
        return revisedTotalCost;
    }
    
    public void setRevisedTotalCost(Double revisedTotalCost) {
        this.revisedTotalCost = revisedTotalCost;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public Date getDeliveryDate() {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }
    
    public String getVendorRemarks() {
        return vendorRemarks;
    }
    
    public void setVendorRemarks(String vendorRemarks) {
        this.vendorRemarks = vendorRemarks;
    }
    
    public String getMaterialFamily() {
        return materialFamily;
    }
    
    public void setMaterialFamily(String materialFamily) {
        this.materialFamily = materialFamily;
    }
}
