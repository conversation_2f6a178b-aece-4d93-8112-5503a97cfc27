package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for approved PO details with line items and grand total")
public class ApprovedPODTO {
    
    @Schema(description = "Purchase Request ID", example = "1")
    private Long prId;
    
    @Schema(description = "Purchase Request ID string", example = "SS/ABC/001/2024")
    private String prIdString;
    
    @Schema(description = "Yard number", example = "YRD001")
    private String yardNumber;
    
    @Schema(description = "Project name", example = "Shipbuilding Project A")
    private String projectName;
    
    @Schema(description = "Contractor/Client name", example = "Oceanic Shipbuilders")
    private String contractorName;
    
    @Schema(description = "List of line items with rates and totals")
    private List<ApprovedPOLineItemDTO> lineItems;
    
    @Schema(description = "Grand total amount calculated from line items", example = "850000.00")
    private BigDecimal grandTotal;
    
    @Schema(description = "Revised grand total if payment has been revised", example = "800000.00")
    private BigDecimal revisedGrandTotal;

    @Schema(description = "Currency for the purchase order", example = "USD")
    private String currency;

    @Schema(description = "Current status of the purchase request", example = "IN_PROGRESS_MANAGEMENT")
    private String status;
}
