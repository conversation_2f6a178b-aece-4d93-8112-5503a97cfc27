package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorDTO {
    private Long srNo;
    private String companyName;
    private String gstNumber;
    private String panNo;
    private String vendorName;
    private String whatsappNumber;
    private String contactNumber;
    private String emailId;
    private List<AddressDTO> addresses; // List of address objects with type
    private String country;
    private String city;
    private String state;
    private String items;
    private String vendorCode;
    private String creditDays;
    private BigDecimal creditLimit;
    private String remark;
    private String vendorType;
    private String status;
}
