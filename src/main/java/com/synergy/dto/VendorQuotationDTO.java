package com.synergy.dto;

import java.util.Date;
import java.util.List;

public class VendorQuotationDTO {
    private Long id;
    private Long purchaseRequestId;
    private Long vendorId;
    private String vendorName;
    private String vendorCompanyName;
    private List<VendorQuotationItemDTO> items;
    private Date submissionDate;
    private Double totalCost;
    private String status;
    private String priority;
    private String currency;
    private String stockPoint;
    private String referenceNumber;
    private List<VendorQuotationChargeDTO> additionalCharges;
    private Double additionalChargesTotal;
    private Double grandTotal;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPurchaseRequestId() {
        return purchaseRequestId;
    }

    public void setPurchaseRequestId(Long purchaseRequestId) {
        this.purchaseRequestId = purchaseRequestId;
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorCompanyName() {
        return vendorCompanyName;
    }

    public void setVendorCompanyName(String vendorCompanyName) {
        this.vendorCompanyName = vendorCompanyName;
    }

    public List<VendorQuotationItemDTO> getItems() {
        return items;
    }

    public void setItems(List<VendorQuotationItemDTO> items) {
        this.items = items;
    }

    public Date getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getStockPoint() {
        return stockPoint;
    }

    public void setStockPoint(String stockPoint) {
        this.stockPoint = stockPoint;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public List<VendorQuotationChargeDTO> getAdditionalCharges() {
        return additionalCharges;
    }

    public void setAdditionalCharges(List<VendorQuotationChargeDTO> additionalCharges) {
        this.additionalCharges = additionalCharges;
    }

    public Double getAdditionalChargesTotal() {
        return additionalChargesTotal;
    }

    public void setAdditionalChargesTotal(Double additionalChargesTotal) {
        this.additionalChargesTotal = additionalChargesTotal;
    }

    public Double getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(Double grandTotal) {
        this.grandTotal = grandTotal;
    }
}
