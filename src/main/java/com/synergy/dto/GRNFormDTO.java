package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for GRN form")
public class GRNFormDTO {
    
    @Schema(description = "Gate Pass ID", example = "PO647-GP01")
    private String gatePassId;
    
    @Schema(description = "Vendor PO ID", example = "647-1")
    private String vendorPoId;
    
    @Schema(description = "Yard number", example = "YD-101")
    private String yardNumber;
    
    @Schema(description = "Project name", example = "Speed Boat")
    private String projectName;
    
    @Schema(description = "Vendor company name", example = "ABC Steel Suppliers")
    private String vendorCompanyName;
    
    @Schema(description = "Quality check flag - true if any item requires quality check", example = "true")
    private Boolean qualityCheck;
    
    @Schema(description = "List of line items for GRN")
    private List<GRNLineItemDTO> lineItems;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "GRN line item details")
    public static class GRNLineItemDTO {
        
        @Schema(description = "Item code", example = "01")
        private String itemCode;
        
        @Schema(description = "Product/Item description", example = "High-strength marine steel")
        private String description;
        
        @Schema(description = "Material family", example = "Steel Plates")
        private String materialFamily;
        
        @Schema(description = "Quantity ordered", example = "13")
        private Integer qtyOrdered;
        
        @Schema(description = "Quantity received", example = "10")
        private Integer qtyReceived;
        
        @Schema(description = "Status", example = "Pending")
        private String status;
        
        @Schema(description = "Location", example = "Yard")
        private String location;
        
        @Schema(description = "Remarks", example = "Enter Remarks")
        private String remarks;
        
        @Schema(description = "Quality check requirement for this item", example = "not required")
        private String qualityCheckRequirement;
        
        @Schema(description = "Unique code for the item", example = "UC001")
        private String uniqueCode;

        @Schema(description = "Attachment images for the line item (array of base64 or URLs)", example = "[\"data:image/jpeg;base64,...\", \"data:image/png;base64,...\"]")
        private List<String> attachmentImages;
    }
}
