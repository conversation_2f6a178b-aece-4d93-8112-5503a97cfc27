package com.synergy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for yard master")
public class YardMasterDTO {
    
    @Schema(description = "Unique identifier for the yard", example = "1")
    private Long id;
    
    @NotBlank(message = "Yard number is required")
    @Size(max = 50, message = "Yard number must not exceed 50 characters")
    @Schema(description = "Unique yard number", example = "YD-101", required = true)
    private String yardNumber;
    
    @NotBlank(message = "Client name is required")
    @Size(max = 255, message = "Client name must not exceed 255 characters")
    @Schema(description = "Name of the client", example = "Oceanic Shipbuilders", required = true)
    private String clientName;
    
    @NotBlank(message = "Project name is required")
    @Size(max = 255, message = "Project name must not exceed 255 characters")
    @Schema(description = "Name of the project", example = "Speed Boat", required = true)
    private String projectName;

    @Schema(description = "Date when the yard was created", example = "2024-01-15T10:30:00Z")
    private Date createdDate;
    
    @Schema(description = "Date when the yard was last updated", example = "2024-01-20T14:45:00Z")
    private Date updatedDate;
}
