package com.synergy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "DTO for PDF generation line item data")
public class PdfGenerationLineItemDTO {
    
    @Schema(description = "Unique code", example = "UC001")
    private String uniqueCode;
    
    @Schema(description = "Item code", example = "01")
    private String itemCode;
    
    @Schema(description = "Product description", example = "High-strength marine steel")
    private String description;
    
    @Schema(description = "Material family", example = "Steel Plates")
    private String materialFamily;
    
    @Schema(description = "Quantity", example = "13")
    private Integer quantity;
    
    @Schema(description = "Unit of measure", example = "TL")
    private String unitOfMeasure;
    
    @Schema(description = "Unit price", example = "3846.15")
    private BigDecimal unitPrice;
    
    @Schema(description = "Rate approved", example = "50000.00")
    private BigDecimal rateApproved;
    
    @Schema(description = "Total amount", example = "650000.00")
    private BigDecimal total;
    
    @Schema(description = "Original unit price from quotation", example = "3846.15")
    private BigDecimal originalUnitPrice;
    
    @Schema(description = "Original total from quotation", example = "650000.00")
    private BigDecimal originalTotal;
}
