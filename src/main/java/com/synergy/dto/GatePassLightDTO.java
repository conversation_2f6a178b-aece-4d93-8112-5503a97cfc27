package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Lightweight data transfer object for gate pass dashboard display")
public class GatePassLightDTO {
    
    @Schema(description = "Gate Pass ID", example = "PO647-1-GP01")
    private String gatePassId;
    
    @Schema(description = "Expected delivery date", example = "2025-03-19T00:00:00.000Z")
    private Date expectedDate;
    
    @Schema(description = "Project name", example = "Speed Boat")
    private String projectName;
    
    @Schema(description = "Yard number", example = "YD-101")
    private String yardNumber;
    
    @Schema(description = "Number of line items", example = "10")
    private Integer lineItemCount;
    
    @Schema(description = "Gate pass status", example = "PENDING")
    private String status;

    // Additional fields for internal use (not displayed in dashboard but needed for actions)
    @Schema(description = "Vendor PO ID", example = "647-1")
    private String vendorPoId;
    
    @Schema(description = "Original Purchase Request ID", example = "647")
    private Long originalPrId;
    
    @Schema(description = "Vendor company name", example = "ABC Steel Suppliers")
    private String vendorCompanyName;
}
