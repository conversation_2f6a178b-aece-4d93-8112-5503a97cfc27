package com.synergy.dto;

public class VendorRevisedQuotationChargeDTO {
    private Long id;
    private String chargeName;
    
    // Original values
    private Double originalChargeAmount;
    
    // Revised values
    private Double revisedChargeAmount;
    
    // Discount information
    private Double discountAmount;
    private Double discountPercentage;
    
    private String chargeType;
    private String description;
    
    // Constructors
    public VendorRevisedQuotationChargeDTO() {}
    
    public VendorRevisedQuotationChargeDTO(String chargeName, Double originalChargeAmount, Double revisedChargeAmount) {
        this.chargeName = chargeName;
        this.originalChargeAmount = originalChargeAmount;
        this.revisedChargeAmount = revisedChargeAmount;
        this.discountAmount = originalChargeAmount - revisedChargeAmount;
        if (originalChargeAmount != null && originalChargeAmount > 0) {
            this.discountPercentage = (discountAmount / originalChargeAmount) * 100;
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getChargeName() {
        return chargeName;
    }
    
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }
    
    public Double getOriginalChargeAmount() {
        return originalChargeAmount;
    }
    
    public void setOriginalChargeAmount(Double originalChargeAmount) {
        this.originalChargeAmount = originalChargeAmount;
    }
    
    public Double getRevisedChargeAmount() {
        return revisedChargeAmount;
    }
    
    public void setRevisedChargeAmount(Double revisedChargeAmount) {
        this.revisedChargeAmount = revisedChargeAmount;
    }
    
    public Double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public Double getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(Double discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public String getChargeType() {
        return chargeType;
    }
    
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
}
