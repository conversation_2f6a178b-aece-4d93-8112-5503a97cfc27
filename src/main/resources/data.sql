-- Create users table

CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(10),
    username VARCHAR(50) NOT NULL UNIQUE,
    user_email VARCHAR(100) NOT NULL UNIQUE,
    employee_name VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL,
    date DATE NOT NULL
);

-- Insert initial users
INSERT INTO users (serial_no, username, user_email, employee_name, password, role, date)
VALUES 
('001', 'admin', '<EMAIL>', 'Admin User', '1234', 'MANAGEMENT', CURRENT_DATE),
('002', 'admin1', '<EMAIL>', 'Engineer User', '1234', 'ENGINEER', CURRENT_DATE);

-- Create Material Requisitions table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='material_requisitions' and xtype='U')
CREATE TABLE material_requisitions (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    requisition_id VARCHAR(50) NOT NULL,
    yard_number VARCHAR(50) NOT NULL,
    contractor_name VARCHAR(255) NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    created_date DATETIME DEFAULT GETDATE(),
    status VARCHAR(20) DEFAULT 'PENDING'
);

-- Create Material Requisition Items table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='material_requisition_items' and xtype='U')
CREATE TABLE material_requisition_items (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    material_requisition_id BIGINT,
    unique_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    unit_of_measure VARCHAR(50) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (material_requisition_id) REFERENCES material_requisitions(id)
);

-- Performance Indexes for Approved Vendor POs API
-- These indexes will significantly improve the performance of the /approved-vendor-pos endpoint

-- Index on purchase_requests.status for faster filtering of approved PRs
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_purchase_requests_status')
BEGIN
    CREATE NONCLUSTERED INDEX idx_purchase_requests_status
    ON purchase_requests(status)
    INCLUDE (id, pr_id, created_date, contractor_name, yard_number, project_name, pr_type, priority, currency);
END;

-- Index on vendor_quotations.purchase_request_id for faster joins
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_vendor_quotations_pr_id')
BEGIN
    CREATE NONCLUSTERED INDEX idx_vendor_quotations_pr_id
    ON vendor_quotations(purchase_request_id)
    INCLUDE (id, vendor_id, submission_date, currency, stock_point, reference_number);
END;

-- Index on vendor_quotation_items.vendor_quotation_id for faster joins
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_vendor_quotation_items_vq_id')
BEGIN
    CREATE NONCLUSTERED INDEX idx_vendor_quotation_items_vq_id
    ON vendor_quotation_items(vendor_quotation_id)
    INCLUDE (id, unique_code, product_name, available_quantity, unit_price, delivery_date, total_cost, selected, vendor_remarks);
END;

-- Index on purchase_request_items.purchase_request_id for faster line item loading
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_purchase_request_items_pr_id')
BEGIN
    CREATE NONCLUSTERED INDEX idx_purchase_request_items_pr_id
    ON purchase_request_items(purchase_request_id)
    INCLUDE (id, unique_code, product_name, quantity, material_family, remarks, attachments, quantity_updated);
END;

-- Composite index on vendor_quotation_items for selected items filtering
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_vendor_quotation_items_selected')
BEGIN
    CREATE NONCLUSTERED INDEX idx_vendor_quotation_items_selected
    ON vendor_quotation_items(vendor_quotation_id, selected)
    WHERE selected = 1
    INCLUDE (id, unique_code, product_name, available_quantity, unit_price, delivery_date, total_cost, vendor_remarks);
END;

-- Index on purchase_requests.created_date for ordering
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_purchase_requests_created_date')
BEGIN
    CREATE NONCLUSTERED INDEX idx_purchase_requests_created_date
    ON purchase_requests(created_date DESC)
    INCLUDE (id, pr_id, status, contractor_name, yard_number, project_name, pr_type);
END;

-- Index on vendor_master.sr_no for faster vendor lookups
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_vendor_master_sr_no')
BEGIN
    CREATE NONCLUSTERED INDEX idx_vendor_master_sr_no
    ON vendor_master(sr_no)
    INCLUDE (company_name, vendor_name, email_id, contact_number, status);
END;

-- Composite index for purchase_request_vendors junction table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_purchase_request_vendors_composite')
BEGIN
    CREATE NONCLUSTERED INDEX idx_purchase_request_vendors_composite
    ON purchase_request_vendors(purchase_request_id, vendor_id);
END;